<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Magazine Style Design</title>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Times+New+Roman:wght@400;700&family=Fira+Code:wght@400;700&family=Red+Hat+Text:wght@400;700&display=swap" rel="stylesheet">
<style>
    /* Scoped styles for the custom section to prevent conflicts */
    #catham-custom-section {
        /* CSS Variables - scoped to this section only */
        --color-dark: #000;
        --color-light: #fff;
        --color-text-primary: #111;
        --color-text-secondary: #333;
        --color-text-tertiary: #555;
        --color-text-quote: #666;
        --color-light-bg: #f9f9f9;
        --font-primary: "Times New Roman", serif;
        --font-secondary: "Red Hat Text", sans-serif;
        --font-fira-code: "Fira Code", monospace;
        --font-tertiary: Helvetica, Arial, sans-serif;

        /* Reset and base styles for this section only */
        width: 100%;
        overflow: hidden;
        background-color: var(--color-dark);
        margin: 0;
        padding: 0;
        color: var(--color-text-primary);
        font-family: var(--font-primary);
        font-size: 14px;
        line-height: 1.6;
        box-sizing: border-box;
    }

    /* All child elements inherit proper box-sizing and reset */
    #catham-custom-section *,
    #catham-custom-section *::before,
    #catham-custom-section *::after {
        box-sizing: border-box;
    }

    /* Prevent this section from affecting global styles */
    #catham-custom-section {
        isolation: isolate; /* Creates new stacking context */
        contain: layout style; /* Contains layout and style changes */
    }

    /* Editorial Separator - Visual break and animation */
    #catham-custom-section .editorial-separator {
        width: 100%;
        padding: 20px 0;
        background-color: var(--color-dark);
        display: flex;
        justify-content: center;
        align-items: center;
    }

    #catham-custom-section .illuminating-line {
        width: 80%;
        max-width: 1200px;
        height: 2px;
        background: linear-gradient(
            90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.3) 20%,
            rgba(255, 255, 255, 0.9) 50%,
            rgba(255, 255, 255, 0.3) 80%,
            transparent 100%
        );
        position: relative;
        overflow: hidden;
    }

    #catham-custom-section .illuminating-line::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0.8) 0%,
            transparent 50%,
            rgba(255, 255, 255, 0.8) 100%
        );
        animation: illuminate 7s ease-in-out infinite;
    }

    @keyframes illuminate {
        0% { left: -100%; opacity: 0; }
        50% { opacity: 1; }
        100% { left: 100%; opacity: 0; }
    }

    /* Main Magazine Container */
    #catham-custom-section .editorial-magazine {
        max-width: 100%;
        margin: 0 auto;
        overflow: hidden;
        background-color: var(--color-dark);
        display: flex;
        flex-direction: column;
        padding-bottom: 0; /* Ensure no bottom padding */
    }

    /* Hero Section - Main visual with key messages */
    #catham-custom-section .hero-section {
        padding: 0;
        margin: 0; /* Ensure no margin */
        background-color: var(--color-dark);
        display: flex;
        flex-direction: column; /* Stack content vertically */
        justify-content: center;
        align-items: center;
        min-height: 600px; /* Default desktop min-height */
        position: relative;
        overflow: hidden;
        /* Removed background-image and filter from here */
    }

    /* New element for the background image with grayscale */
    #catham-custom-section .hero-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('https://cdn.shopify.com/s/files/1/0909/6560/6735/files/newyorkcity.jpg?v=1751629897');
        background-size: cover;
        background-position: center 80%; /* Adjusted desktop background position */
        background-repeat: no-repeat;
        filter: grayscale(70%); /* Applied grayscale only to the background image */
        z-index: 1; /* Below content and overlay */
    }

    /* Pseudo-element for dark grayscale overlay with black gradient at bottom */
    #catham-custom-section .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            to bottom,
            rgba(0, 0, 0, 0.7) 0%,
            rgba(0, 0, 0, 0.7) 70%,
            rgba(0, 0, 0, 1) 100%
        ); /* Dark overlay with black gradient to bottom */
        z-index: 2; /* Below content, above background */
    }

    /* Minimalist Fashion Card Layout styles */
    #catham-custom-section .minimalist-fashion-cards {
        width: 100%;
        max-width: 1000px; /* Constrain overall width */
        margin: 0 auto; /* Center the cards horizontally */
        position: relative; /* Changed to relative for z-index context */
        padding: 10px 20px; /* Reduced padding for desktop */
        box-sizing: border-box;
        z-index: 3; /* Ensure it's above the overlay */
        display: flex;
        flex-direction: column; /* Stack children vertically */
        gap: 0px !important; /* Force gap to 0 */
        justify-content: center;
        align-items: center;
    }

    /* Styling for the individual content blocks within minimalist-fashion-cards */
    #catham-custom-section .minimalist-fashion-cards > div {
        width: 100%; /* Take full width */
        background-color: transparent; /* No background on these containers */
        position: relative; /* Set position relative for z-index to work on siblings */
        display: flex;
        flex-direction: column;
        overflow: hidden;
        border: none;
        box-shadow: none;
        margin: 0 !important; /* Force margin to 0 */
    }

    /* Specific styling for the image container (first child) */
    #catham-custom-section .minimalist-fashion-cards > div:first-child { /* Image container */
        aspect-ratio: 1/1; /* Adjusted aspect ratio for desktop image to be square */
        height: 84px !important;
        min-height: 84px !important;
        justify-content: center; /* Center image vertically */
        align-items: center; /* Center image horizontally */
        margin-top: 140px !important; /* Moved image down by ~1cm for desktop */
        margin-bottom: 2px !important;
        z-index: 2; /* Ensure image is above the brand name text */
    }

    /* Specific styling for the text content container (second child) */
    #catham-custom-section .minimalist-fashion-cards > div:last-child { /* Text content container */
        padding: 15px 30px;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.9)); /* Dark gradient */
        justify-content: flex-start; /* Align content to the top (h2) */
        align-items: center; /* Center items horizontally within the card */
    }

    #catham-custom-section .minimalist-fashion-cards .brand-name {
        font-family: var(--font-primary); /* Using primary font */
        font-size: 3.5rem; /* Adjusted font size for desktop to prevent cutoff */
        line-height: 1; /* Tight line height */
        color: var(--color-light); /* White text */
        letter-spacing: 2px;
        /* Updated font weight for CATHÁM NEW YORK */
        font-weight: 500;
        text-transform: uppercase;
        z-index: 1; /* Placed behind the image */
        text-align: center; /* Ensure text is centered within its own box */
        margin: 0 !important;
        padding: 0 !important;
        position: absolute; /* Allows positioning relative to parent */
        top: -110px; /* Adjusted to place it higher, more behind the image */
        left: 50%;
        transform: translateX(-50%); /* Centered horizontally */
        white-space: nowrap; /* Prevent text from wrapping */
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5); /* Added text shadow for desktop */
    }

    #catham-custom-section .minimalist-fashion-cards img {
        margin-top: 30px;
        width: 100%;
        height: 100%;
        object-fit: contain; /* Changed to contain to prevent cropping */
        transition: transform 0.3s ease; /* Add transition for smooth scaling */
    }

    #catham-custom-section .minimalist-fashion-cards img:hover {
        transform: scale(1.05); /* Slightly scale up on hover */
    }

    #catham-custom-section .minimalist-fashion-cards h2 {
        font-family: var(--font-secondary); /* Changed to Red Hat Text */
        font-size: 0.9rem;
        color: var(--color-light); /* Light text */
        letter-spacing: 2px;
        font-weight: 400;
        text-transform: uppercase;
        margin-bottom: 70px; /* Space below the H2 */
        margin-top: 0; /* Remove default top margin */
        text-align: center; /* Center the H2 text */
    }

    #catham-custom-section .centered-content-group {
        display: flex;
        flex-direction: column;
        justify-content: center; /* Center content vertically within this group */
        align-items: center; /* Center content horizontally within this group */
        flex-grow: 1; /* Allow this group to take up remaining space */
        width: 100%; /* Ensure it takes full width of its parent for centering */
        padding-top: 20px; /* Add some space from the H2 */
    }

    #catham-custom-section .minimalist-fashion-cards .catham-tagline {
        font-family: var(--font-primary); /* Remains Times New Roman */
        /* Updated font weight for CATHÁM NEW YORK */
        font-weight: 520 !important;
        font-size: 4.5rem;
        color: var(--color-light); /* Light text */
        line-height: 1.2;
        margin-bottom: 20px;
        text-align: center;
    }

    #catham-custom-section .minimalist-fashion-cards .catham-tagline span {
        font-size: 1.4rem;
        /* Added font weight for subheading */
        font-weight: 400;
    }

    #catham-custom-section .minimalist-fashion-cards .cta-buttons {
        display: flex;
        justify-content: center; /* Center buttons horizontally */
        gap: 10px;
        margin-top: 20px;
        margin-bottom: 0; /* Removed bottom margin that might cause white line */
    }

    #catham-custom-section .minimalist-fashion-cards .cta-buttons a {
        display: inline-block;
        padding: 10px 20px;
        background-color: var(--color-light);
        color: var(--color-dark);
        text-decoration: none;
        font-family: var(--font-secondary); /* Changed to Red Hat Text */
        font-size: 0.9rem;
        letter-spacing: 1.5px;
        font-weight: 400;
        text-transform: uppercase;
        /* Removed border-radius for sharp corners */
        border-radius: 0;
        transition: all 0.3s ease;
        text-align: center;
    }

    #catham-custom-section .minimalist-fashion-cards .cta-buttons a:hover {
        background-color: #eee;
        /* Added box-shadow and transform for premium hover effect */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        transform: translateY(-2px);
    }

    #catham-custom-section .minimalist-fashion-cards .cta-buttons a:last-child {
        background-color: transparent;
        color: var(--color-light);
        border: 1px solid var(--color-light);
    }

    #catham-custom-section .minimalist-fashion-cards .cta-buttons a:last-child:hover {
        background-color: rgba(255, 255, 255, 0.1);
        /* Added box-shadow and transform for premium hover effect */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        transform: translateY(-2px);
    }

    #catham-custom-section .minimalist-fashion-cards .product-attributes {
        text-align: center;
        margin-bottom: 20px; /* Moved margin to bottom to create space before buttons */
        margin-top: 0; /* Ensure no extra top margin */
        display: flex; /* Use flexbox to center content */
        justify-content: center;
        align-items: center;
        flex-wrap: wrap; /* Allow wrapping on small screens */
    }

    /* Styling for individual animated product attribute items */
    #catham-custom-section .product-attributes .attribute-item {
        opacity: 0;
        transform: translateY(10px);
        animation: fadeInSlideUp 0.6s ease-out forwards;
        font-family: var(--font-secondary); /* Changed to Red Hat Text */
        font-size: 0.65rem;
        color: rgba(255, 255, 255, 0.6); /* Lighter text */
        letter-spacing: 1px;
        font-weight: 400;
        white-space: nowrap; /* Keep phrases on one line */
    }

    /* Styling for the static bullet separators */
    #catham-custom-section .product-attributes .attribute-separator {
        font-family: var(--font-secondary); /* Changed to Red Hat Text */
        color: rgba(255, 255, 255, 0.6);
        margin: 0 8px; /* Space around the bullet */
        font-size: 0.65rem;
        font-weight: 400;
        white-space: nowrap;
    }

    /* Animation delays for sequential appearance */
    #catham-custom-section .product-attributes .attribute-item:nth-child(1) { /* COLLAGEN ACTIVATING */
        animation-delay: 0.2s;
    }
    #catham-custom-section .product-attributes .attribute-item:nth-child(3) { /* ECOCERT CERTIFIED */
        animation-delay: 0.5s;
    }
    #catham-custom-section .product-attributes .attribute-item:nth-child(5) { /* NORDIC BOTANICALS */
        animation-delay: 0.8s;
    }

    /* Keyframe animation for fade-in and slide-up effect */
    @keyframes fadeInSlideUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Responsive Adjustments for Mobile */
    @media (max-width: 992px) {
        #catham-custom-section .hero-section {
            min-height: 450px;
            padding: 20px 0;
        }
        #catham-custom-section .hero-background {
            background-position: center 80%; /* Maintain desktop background position */
        }
        #catham-custom-section .minimalist-fashion-cards {
            padding: 20px 15px;
            gap: 15px;
        }

        #catham-custom-section .minimalist-fashion-cards img {
            margin-top: 0px;
            width: 100%;
            height: 100%;
            object-fit: contain; /* Changed to contain to prevent cropping */
            transition: transform 0.3s ease; /* Add transition for smooth scaling */
            max-width: 100%; /* Ensure image doesn't overflow container */
        }

        #catham-custom-section .minimalist-fashion-cards > div:first-child { /* Image container */
            height: 84px !important;
            min-height: 84px !important;
        }
        #catham-custom-section .minimalist-fashion-cards > div:last-child { /* Text content container */
            padding: 20px 20px;
        }
    }

    @media (max-width: 768px) {
        #catham-custom-section .hero-section {
            height: auto;
            min-height: 400px;
            padding: 10px;
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        #catham-custom-section .hero-background {
            background-position: center top; /* Adjusted background position for mobile */
        }
        #catham-custom-section .minimalist-fashion-cards {
            padding: 0 0px; /* Added horizontal padding for mobile */
            height: auto;
            width: 100%;
            display: flex;
            flex-direction: column;
            margin-top: 20px;
            max-width: 95%;
            gap: 15px;
        }
        #catham-custom-section .minimalist-fashion-cards > div {
            flex: none;
            width: 100%;
            max-width: none;
            margin: 0 !important;
        }
        #catham-custom-section .minimalist-fashion-cards > div:first-child { /* Image container */
            aspect-ratio: 3/4;
            height: 180px !important; /* Significantly increased height for mobile image */
            min-height: 180px !important; /* Significantly increased min-height for mobile image */
            margin-top: 100px !important; /* Moved image down by ~1cm for mobile */
            margin-bottom: 1px !important;
            z-index: 2; /* Ensure image is above the brand name text */
        }
        #catham-custom-section .minimalist-fashion-cards .brand-name {
            font-size: 0.9rem; /* Adjusted font size for mobile readability */
            line-height: 0.9;
            top: -160px; /* Adjusted to place it higher on mobile */
            left: 50%;
            transform: translateX(-50%);
            letter-spacing: 2px;
            white-space: nowrap; /* Prevent text from wrapping */
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5); /* Added text shadow for mobile */
        }
        /* Hide the h2 on mobile */
        #catham-custom-section .minimalist-fashion-cards h2 {
            display: none;
        }
        #catham-custom-section .minimalist-fashion-cards .catham-tagline {
            font-size: 1.7rem;
            margin-bottom: 10px;
            line-height: 1.2;
        }
        #catham-custom-section .minimalist-fashion-cards .catham-tagline span {
            font-size: 0.95rem;
        }
        #catham-custom-section .minimalist-fashion-cards .product-attributes {
            font-size: 0.5rem;
            margin-top: 10px;
            margin-bottom: 10px;
            line-height: 1.3;
        }
        #catham-custom-section .minimalist-fashion-cards .cta-buttons {
            flex-direction: row; /* Changed to row for side-by-side buttons on mobile */
            gap: 10px;
            margin-top: 0;
            margin-bottom: 0; /* Removed bottom margin that might cause white line */
        }
        #catham-custom-section .minimalist-fashion-cards .cta-buttons a {
            width: auto; /* Allow buttons to size based on content */
            max-width: none; /* Remove max-width constraint */
            padding: 10px 15px;
            font-size: 0.7rem;
        }
    }
</style>


<!-- Catham Custom Section - Isolated from other site styles -->
<div id="catham-custom-section">
    <main class="editorial-magazine">
        <!-- Editorial Separator with Illuminating Line Effect -->
        <div class="editorial-separator">
            <div class="illuminating-line"></div>
        </div>

        <!-- Hero Section with Image and Minimalist Fashion Cards -->
        <section class="hero-section">
            <!-- New div for the background image with grayscale effect -->
            <div class="hero-background"></div>

            <!-- Minimalist Fashion Card Layout -->
            <div class="minimalist-fashion-cards">
                <!-- Brand Name (moved above the image) -->
                <div class="brand-name">
                    <!-- Text content for brand name can go here if needed -->
                </div>
                <!-- Top Section - Product Image -->
                <div class="brand-img">
                    <!-- Main Image (this will remain in full color) -->
                    <img src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/catham_new_york_face_elasticity_skincare_4.png?v=1752682811" alt="Catham New York Skincare">
                </div>

                <!-- Bottom Section - Brand Story and other content -->
                <div>
                    <!-- H2 (Rooted in nature...) is now at the top of this section -->
                    <h2>
                        Rooted in nature. refined for the city.
                    </h2>
                    <!-- Group for centered content -->
                    <div class="centered-content-group">
                        <div class="catham-tagline">
                            CATHÁM NEW YORK:<br><span>Collagen-Smart Skincare. For Lifted Complexion.</span>
                            <br><br>
                        </div>
                        <!-- Product Attributes (now with animated items) -->
                        <div class="product-attributes">
                            <span class="attribute-item">COLLAGEN ACTIVATING</span>
                            <span class="attribute-separator">•</span>
                            <span class="attribute-item">ECOCERT CERTIFIED</span>
                            <span class="attribute-separator">•</span>
                            <span class="attribute-item">NORDIC BOTANICALS</span>
                        </div>
                        <div class="cta-buttons">
                            <a href="/collections/all">SHOP NOW</a>
                            <a href="/pages/about-collagen-skincare">OUR STORY</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
</div>
