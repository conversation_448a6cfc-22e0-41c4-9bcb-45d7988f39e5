<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>The Stress-Aging Skin Guide | CathamLift™ Morning Ritual</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        background: #ffffff;
        color: #000000;
        font-family: "Georgia", "Times New Roman", serif;
        line-height: 1.6;
        overflow-x: hidden;
      }

      /* Floating Navigation */
      .floating-nav {
        position: fixed;
        top: 50%;
        right: 30px;
        transform: translateY(-50%);
        z-index: 1000;
        background: rgba(0, 0, 0, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 25px;
        padding: 15px 8px;
        transition: all 0.3s ease;
      }

      .floating-nav:hover {
        background: rgba(0, 0, 0, 0.95);
        transform: translateY(-50%) scale(1.05);
      }

      .nav-item {
        display: block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.4);
        margin: 8px 0;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
      }

      .nav-item:hover {
        background: #d4af37;
        transform: scale(1.3);
      }

      .nav-item.active {
        background: #ffffff;
      }

      .nav-tooltip {
        position: absolute;
        right: 25px;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s ease;
      }

      .nav-item:hover .nav-tooltip {
        opacity: 1;
      }

      .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 40px;
      }

      /* Full-width sections */
      .full-width {
        width: 100vw;
        position: relative;
        left: 50%;
        right: 50%;
        margin-left: -50vw;
        margin-right: -50vw;
      }

      /* Hero Section - Chanel No.5 inspired */
      .hero-section {
        background: linear-gradient(
          135deg,
          #000000 0%,
          #1a1a1a 50%,
          #000000 100%
        );
        color: #ffffff;
        padding: 120px 0;
        text-align: center;
        position: relative;
        overflow: hidden;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .hero-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(
          circle at 30% 70%,
          rgba(212, 175, 55, 0.1) 0%,
          transparent 50%
        );
        opacity: 0.6;
      }

      .hero-content {
        position: relative;
        z-index: 2;
        max-width: 900px;
        margin: 0 auto;
      }

      .hero-title {
        font-size: 4.5rem;
        font-weight: 300;
        letter-spacing: 3px;
        margin-bottom: 30px;
        text-transform: uppercase;
        opacity: 0;
        animation: fadeInUp 1.5s ease forwards;
      }

      .hero-subtitle {
        font-size: 1.4rem;
        font-weight: 300;
        letter-spacing: 1px;
        opacity: 0.9;
        max-width: 700px;
        margin: 0 auto 40px;
        line-height: 1.8;
        opacity: 0;
        animation: fadeInUp 1.5s ease 0.5s forwards;
      }

      .hero-usp {
        font-size: 1.1rem;
        color: #d4af37;
        font-style: italic;
        margin-top: 30px;
        opacity: 0;
        animation: fadeInUp 1.5s ease 1s forwards;
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* Section Styling - Magazine Layout */
      .section {
        padding: 100px 0;
        position: relative;
      }

      .section-dark {
        background: #000000;
        color: #ffffff;
      }

      .section-beige {
        background: #f5f5f0;
        color: #000000;
      }

      .section-gold {
        background: linear-gradient(135deg, #f5f5f0 0%, #ede8d8 100%);
        color: #000000;
      }

      .section-title {
        font-size: 3.2rem;
        font-weight: 300;
        text-align: center;
        margin-bottom: 60px;
        letter-spacing: 2px;
        text-transform: uppercase;
        position: relative;
      }

      .section-title::after {
        content: "";
        position: absolute;
        bottom: -20px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 1px;
        background: #d4af37;
      }

      .section-subtitle {
        font-size: 1.6rem;
        font-weight: 300;
        margin-bottom: 40px;
        text-align: center;
        opacity: 0.8;
        font-style: italic;
      }

      /* Magazine Split Layout */
      .magazine-split {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 80px;
        align-items: center;
        margin: 80px 0;
        min-height: 60vh;
      }

      .magazine-split.reverse {
        direction: rtl;
      }

      .magazine-split.reverse > * {
        direction: ltr;
      }

      .split-content {
        padding: 40px;
      }

      .split-image {
        position: relative;
        height: 500px;
        background: #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #e0e0e0;
        overflow: hidden;
      }

      .split-image::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          45deg,
          transparent 49%,
          #d4af37 50%,
          transparent 51%
        );
        opacity: 0.1;
      }

      .illustration-placeholder {
        width: 80%;
        height: 80%;
        background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
        border: 2px dashed #ccc;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.1rem;
        color: #666;
        text-align: center;
        transition: all 0.3s ease;
      }

      .illustration-placeholder:hover {
        transform: scale(1.05);
        border-color: #d4af37;
        color: #d4af37;
      }

      /* Interactive Step Cards with Accordion */
      .step-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 40px;
        margin: 80px 0;
      }

      .step-card {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 0;
        transition: all 0.4s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      }

      .step-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        border-color: #d4af37;
      }

      .step-card.dark {
        background: #000000;
        color: #ffffff;
        border-color: #333;
      }

      .step-card.dark:hover {
        border-color: #d4af37;
      }

      .step-header {
        padding: 30px;
        position: relative;
        border-bottom: 1px solid #f0f0f0;
      }

      .step-card.dark .step-header {
        border-bottom-color: #333;
      }

      .step-number {
        font-size: 4rem;
        font-weight: 100;
        opacity: 0.2;
        position: absolute;
        top: 10px;
        right: 20px;
        line-height: 1;
      }

      .step-title {
        font-size: 1.4rem;
        font-weight: 400;
        margin-bottom: 10px;
        text-transform: uppercase;
        letter-spacing: 2px;
        position: relative;
        z-index: 2;
      }

      .time-indicator {
        background: #d4af37;
        color: #000000;
        padding: 6px 16px;
        font-size: 0.85rem;
        display: inline-block;
        margin-bottom: 15px;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-weight: 500;
      }

      .step-preview {
        font-size: 1rem;
        line-height: 1.6;
        opacity: 0.8;
        margin-bottom: 15px;
      }

      .expand-indicator {
        position: absolute;
        bottom: 15px;
        right: 20px;
        font-size: 1.2rem;
        opacity: 0.6;
        transition: transform 0.3s ease;
      }

      .step-card.expanded .expand-indicator {
        transform: rotate(180deg);
      }

      .step-content {
        padding: 0 30px;
        max-height: 0;
        overflow: hidden;
        transition: all 0.4s ease;
        opacity: 0;
      }

      .step-card.expanded .step-content {
        max-height: 500px;
        padding: 30px;
        opacity: 1;
      }

      .step-content p {
        margin-bottom: 15px;
        font-size: 1rem;
        line-height: 1.7;
      }

      .step-content strong {
        color: #d4af37;
        font-weight: 500;
      }

      .step-card.dark .step-content strong {
        color: #d4af37;
      }

      /* Luxury Interactive Checklist */
      .interactive-checklist {
        background: linear-gradient(135deg, #f5f5f0 0%, #ede8d8 100%);
        padding: 60px;
        margin: 80px 0;
        border: 1px solid #d4af37;
        position: relative;
        max-width: 900px;
        margin-left: auto;
        margin-right: auto;
      }

      .interactive-checklist::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          45deg,
          transparent 49%,
          rgba(212, 175, 55, 0.1) 50%,
          transparent 51%
        );
        pointer-events: none;
      }

      .checklist-item {
        display: flex;
        align-items: center;
        margin: 20px 0;
        cursor: pointer;
        transition: all 0.3s ease;
        padding: 15px 20px;
        border-radius: 0;
        background: rgba(255, 255, 255, 0.7);
        border: 1px solid transparent;
      }

      .checklist-item:hover {
        background: rgba(255, 255, 255, 0.9);
        border-color: #d4af37;
        transform: translateX(10px);
      }

      .checkbox {
        width: 24px;
        height: 24px;
        border: 2px solid #000000;
        margin-right: 20px;
        position: relative;
        transition: all 0.3s ease;
        background: #ffffff;
      }

      .checkbox.checked {
        background: #d4af37;
        border-color: #d4af37;
      }

      .checkbox.checked::after {
        content: "✓";
        color: #000000;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 14px;
        font-weight: bold;
      }

      .checklist-text {
        font-size: 1.1rem;
        font-weight: 400;
        letter-spacing: 0.5px;
      }

      /* Luxury CTA Section */
      .cta-section {
        background: linear-gradient(
          135deg,
          #000000 0%,
          #1a1a1a 50%,
          #000000 100%
        );
        color: #ffffff;
        padding: 120px 0;
        text-align: center;
        position: relative;
        overflow: hidden;
      }

      .cta-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(
          circle at 70% 30%,
          rgba(212, 175, 55, 0.1) 0%,
          transparent 50%
        );
        opacity: 0.8;
      }

      .cta-content {
        position: relative;
        z-index: 2;
        max-width: 800px;
        margin: 0 auto;
      }

      .cta-button {
        display: inline-block;
        background: transparent;
        color: #d4af37;
        border: 2px solid #d4af37;
        padding: 18px 50px;
        text-decoration: none;
        text-transform: uppercase;
        letter-spacing: 3px;
        font-weight: 400;
        transition: all 0.4s ease;
        margin-top: 40px;
        position: relative;
        overflow: hidden;
      }

      .cta-button::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: #d4af37;
        transition: left 0.4s ease;
        z-index: -1;
      }

      .cta-button:hover::before {
        left: 0;
      }

      .cta-button:hover {
        color: #000000;
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(212, 175, 55, 0.3);
      }

      .download-button {
        background: #d4af37;
        color: #000000;
        border: 2px solid #d4af37;
        margin-left: 20px;
      }

      .download-button:hover {
        background: transparent;
        color: #d4af37;
      }

      /* Enhanced Two Column Layout */
      .two-column {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 60px;
        align-items: center;
        margin: 60px 0;
      }

      .column-text {
        font-size: 1.2rem;
        line-height: 1.8;
        font-weight: 300;
      }

      .highlight-box {
        background: linear-gradient(135deg, #f5f5f0 0%, #ede8d8 100%);
        padding: 40px;
        border-left: 4px solid #d4af37;
        margin: 40px 0;
        position: relative;
      }

      .highlight-box.dark {
        background: linear-gradient(135deg, #1a1a1a 0%, #0d0d0d 100%);
        color: #ffffff;
        border-left-color: #d4af37;
      }

      /* Scroll Animations */
      .fade-in {
        opacity: 0;
        transform: translateY(50px);
        transition: all 0.8s ease;
      }

      .fade-in.visible {
        opacity: 1;
        transform: translateY(0);
      }

      .slide-in-left {
        opacity: 0;
        transform: translateX(-50px);
        transition: all 0.8s ease;
      }

      .slide-in-left.visible {
        opacity: 1;
        transform: translateX(0);
      }

      .slide-in-right {
        opacity: 0;
        transform: translateX(50px);
        transition: all 0.8s ease;
      }

      .slide-in-right.visible {
        opacity: 1;
        transform: translateX(0);
      }

      /* Responsive Design */
      @media (max-width: 1200px) {
        .container {
          padding: 0 30px;
        }

        .floating-nav {
          right: 20px;
        }
      }

      @media (max-width: 768px) {
        .hero-title {
          font-size: 3rem;
          letter-spacing: 1px;
        }

        .hero-subtitle {
          font-size: 1.2rem;
        }

        .section-title {
          font-size: 2.5rem;
        }

        .step-grid {
          grid-template-columns: 1fr;
          gap: 30px;
        }

        .magazine-split {
          grid-template-columns: 1fr;
          gap: 40px;
        }

        .two-column {
          grid-template-columns: 1fr;
          gap: 30px;
        }

        .container {
          padding: 0 20px;
        }

        .floating-nav {
          display: none;
        }

        .section {
          padding: 60px 0;
        }

        .interactive-checklist {
          padding: 40px 20px;
        }
      }

      @media (max-width: 480px) {
        .hero-title {
          font-size: 2.2rem;
        }

        .step-card {
          margin: 0 -10px;
        }

        .cta-button {
          padding: 15px 30px;
          font-size: 0.9rem;
        }

        .download-button {
          margin-left: 0;
          margin-top: 15px;
          display: block;
        }
      }

      /* Smooth Scrolling */
      html {
        scroll-behavior: smooth;
      }

      /* Enhanced List Styling */
      .styled-list {
        list-style: none;
        padding: 0;
      }

      .styled-list li {
        padding: 15px 0;
        border-bottom: 1px solid rgba(212, 175, 55, 0.2);
        position: relative;
        padding-left: 40px;
        font-size: 1.1rem;
        line-height: 1.7;
      }

      .styled-list li::before {
        content: "→";
        position: absolute;
        left: 0;
        font-weight: bold;
        color: #d4af37;
        font-size: 1.2rem;
      }

      /* Luxury Quote Styling */
      .luxury-quote {
        font-size: 1.8rem;
        font-style: italic;
        text-align: center;
        margin: 60px 0;
        color: #d4af37;
        font-weight: 300;
        letter-spacing: 1px;
      }

      .luxury-quote::before,
      .luxury-quote::after {
        content: '"';
        font-size: 2.5rem;
        opacity: 0.5;
      }

      /* Progress Indicator */
      .progress-bar {
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 3px;
        background: linear-gradient(90deg, #d4af37 0%, #f4e4a6 100%);
        z-index: 9999;
        transition: width 0.3s ease;
      }
    </style>
  </head>
  <body>
    <!-- Progress Bar -->
    <div class="progress-bar" id="progressBar"></div>

    <!-- Floating Navigation -->
    <nav class="floating-nav" id="floatingNav">
      <a href="#hero" class="nav-item active" data-section="hero">
        <span class="nav-tooltip">Introduction</span>
      </a>
      <a href="#why-stress" class="nav-item" data-section="why-stress">
        <span class="nav-tooltip">Why Stress Ages</span>
      </a>
      <a
        href="#cathamlift-intro"
        class="nav-item"
        data-section="cathamlift-intro"
      >
        <span class="nav-tooltip">CathamLift™</span>
      </a>
      <a href="#ritual-steps" class="nav-item" data-section="ritual-steps">
        <span class="nav-tooltip">8 Steps</span>
      </a>
      <a href="#synergy" class="nav-item" data-section="synergy">
        <span class="nav-tooltip">Synergy</span>
      </a>
      <a href="#checklist" class="nav-item" data-section="checklist">
        <span class="nav-tooltip">Checklist</span>
      </a>
      <a href="#cta" class="nav-item" data-section="cta">
        <span class="nav-tooltip">Get Started</span>
      </a>
    </nav>

    <!-- Hero Section -->
    <section id="hero" class="hero-section full-width">
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">The Stress-Aging Skin Guide</h1>
          <p class="hero-subtitle">
            The Exclusive CathamLift™ Morning Ritual – Instantly Lift, Firm &
            De-Puff in 5–6 Minutes
          </p>
          <p class="hero-usp">
            CathamLift™ lifts the surface. Tri-CollagenLift™ awakens cellular
            youth.
          </p>
        </div>
      </div>
    </section>

    <!-- Why Stress Ages Section -->
    <section id="why-stress" class="section section-beige full-width">
      <div class="container">
        <h2 class="section-title fade-in">Why Stress is Aging Your Face</h2>

        <div class="magazine-split">
          <div class="split-content slide-in-left">
            <p class="luxury-quote">
              Here's the truth: stress doesn't just make you feel exhausted –
              it's showing up on your face.
            </p>

            <div class="highlight-box">
              <ul class="styled-list">
                <li>
                  <strong>High cortisol</strong> breaks down collagen and
                  elastin – the very proteins that keep your skin firm, lifted,
                  and smooth.
                </li>
                <li>
                  <strong>Long hours at a desk</strong>, endless screen time,
                  and a neck that's constantly tilting forward (hello,
                  tech-neck) accelerate aging every single day.
                </li>
              </ul>
            </div>

            <p
              style="
                font-size: 1.3rem;
                margin-top: 40px;
                font-style: italic;
                font-weight: 300;
                color: #d4af37;
              "
            >
              But here's the good news: you can reset it – daily.
            </p>
          </div>

          <div class="split-image slide-in-right">
            <div class="illustration-placeholder">
              <div style="text-align: center">
                <strong>Stress-Aging Illustration</strong><br />
                <small
                  >Face diagram showing stress points<br />
                  (Cortisol effects, tech-neck, tension areas)</small
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CathamLift Introduction -->
    <section id="cathamlift-intro" class="section section-dark full-width">
      <div class="container">
        <h2 class="section-title fade-in">Enter: CathamLift™</h2>

        <div class="magazine-split reverse">
          <div class="split-image slide-in-left">
            <div class="illustration-placeholder">
              <div style="text-align: center; color: #666">
                <strong>CathamLift™ Ritual Illustration</strong><br />
                <small
                  >5-6 minute morning routine<br />
                  (Before/after transformation visual)</small
                >
              </div>
            </div>
          </div>

          <div class="split-content slide-in-right">
            <p class="luxury-quote" style="color: #d4af37">
              This isn't a 30-minute spa session you don't have time for.
            </p>

            <p
              style="
                font-size: 1.2rem;
                line-height: 1.8;
                margin-bottom: 40px;
                color: #ffffff;
              "
            >
              CathamLift™ is the exclusive 5–6 minute morning ritual created by
              Cathám New York to instantly lift, firm, and de-puff your face and
              neck – no matter how stressed your life is.
            </p>

            <div class="highlight-box dark">
              <p style="font-size: 1.1rem; margin-bottom: 30px; color: #ffffff">
                <strong style="color: #d4af37"
                  >It's science-backed. It's fast.</strong
                >
                And when paired with our Tri-CollagenLift™ bundle, it works at
                two levels:
              </p>
              <div class="two-column">
                <div>
                  <h3 style="color: #d4af37; margin-bottom: 15px">
                    1. Instant
                  </h3>
                  <p style="color: #ffffff">
                    You visibly tighten and de-puff with targeted muscle release
                    and drainage.
                  </p>
                </div>
                <div>
                  <h3 style="color: #d4af37; margin-bottom: 15px">
                    2. Cumulative
                  </h3>
                  <p style="color: #ffffff">
                    The Tri-CollagenLift™ products rebuild collagen and
                    resilience daily.
                  </p>
                </div>
              </div>
            </div>

            <p
              style="
                font-size: 1.3rem;
                margin-top: 40px;
                font-style: italic;
                color: #d4af37;
              "
            >
              This is your "control-alt-delete" for stress-aging.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- The Ritual Steps -->
    <section id="ritual-steps" class="section section-gold full-width">
      <div class="container">
        <h2 class="section-title fade-in">The CathamLift™ Morning Ritual</h2>
        <p class="section-subtitle fade-in">
          Step-by-Step Guide to Instantly Lift, Firm & De-Puff in 5–6 Minutes
        </p>

        <div class="step-grid">
          <!-- Step 1 -->
          <div class="step-card" onclick="toggleStep(this)">
            <div class="step-header">
              <div class="step-number">01</div>
              <div class="time-indicator">2 Minutes</div>
              <h3 class="step-title">Posture & Tension Release</h3>
              <p class="step-preview">
                Release built-up tension in shoulders, neck, and skull base to
                prepare for facial work.
              </p>
              <div class="expand-indicator">▼</div>
            </div>
            <div class="step-content">
              <p><strong>A. Shoulder Blade Squeeze (30 sec)</strong></p>
              <p>
                Sit or stand tall. Squeeze shoulder blades gently together. Hold
                2–3 seconds. Repeat 5x.
              </p>

              <p><strong>B. Shoulder Stretch (30 sec)</strong></p>
              <p>
                Raise one arm to shoulder height across your body. Gently pull
                with other hand for 20–30 seconds each side.
              </p>

              <p><strong>C. Suboccipital Release (30 sec)</strong></p>
              <p>
                Press fingertips under skull base, make small circles or hold
                pressure for 20–30 seconds.
              </p>

              <p><strong>D. Neck & Shoulder Release (30 sec)</strong></p>
              <p>
                Slowly tilt head toward one shoulder, hold 15 seconds. Rotate to
                other side. Combine with deep breathing.
              </p>
            </div>
          </div>

          <!-- Step 2 -->
          <div class="step-card dark" onclick="toggleStep(this)">
            <div class="step-header">
              <div class="step-number">02</div>
              <div class="time-indicator">1 Minute</div>
              <h3 class="step-title">Lymph Activation Taps</h3>
              <p class="step-preview">
                Activate lymphatic drainage points to prepare for fluid release.
              </p>
              <div class="expand-indicator">▼</div>
            </div>
            <div class="step-content">
              <ul class="styled-list" style="color: #ffffff">
                <li>
                  Tap gently above collarbones (supraclavicular nodes) 10–15x
                </li>
                <li>Tap under jaw (submandibular nodes) 10–12x</li>
                <li>Tap behind ears 8x each</li>
              </ul>
              <p style="margin-top: 15px; font-style: italic; color: #d4af37">
                These taps "unlock" your lymph system, priming fluid release and
                muscle relaxation.
              </p>
            </div>
          </div>

          <!-- Step 3 -->
          <div class="step-card" onclick="toggleStep(this)">
            <div class="step-header">
              <div class="step-number">03</div>
              <div class="time-indicator">45 Seconds</div>
              <h3 class="step-title">Face-Yoga Neck & Jaw Stretch</h3>
              <p class="step-preview">
                Target deep neck fascia and jaw muscles to reverse sagging.
              </p>
              <div class="expand-indicator">▼</div>
            </div>
            <div class="step-content">
              <p>
                Tilt your head back, push it slightly at a 45° angle toward one
                shoulder.
              </p>
              <p>Hold for 3 seconds, then switch sides.</p>
              <p>Repeat 10–20 times.</p>
              <p style="margin-top: 15px; font-style: italic; color: #d4af37">
                Targets deep neck fascia and jaw muscles, reversing sag and
                improving firmness.
              </p>
            </div>
          </div>

          <!-- Step 4 -->
          <div class="step-card dark" onclick="toggleStep(this)">
            <div class="step-header">
              <div class="step-number">04</div>
              <div class="time-indicator">1 Minute</div>
              <h3 class="step-title">Chin Press Lift</h3>
              <p class="step-preview">
                Firm and drain the jawline, targeting cortisol-induced
                puffiness.
              </p>
              <div class="expand-indicator">▼</div>
            </div>
            <div class="step-content">
              <p>Curl both hands into loose fists and place under your chin.</p>
              <p>
                Press your chin downward into your fists – resist – for 3
                seconds.
              </p>
              <p>Release and repeat 10x.</p>
              <p style="margin-top: 15px; font-style: italic; color: #d4af37">
                Firms and drains the jawline, a common puffiness hotspot when
                cortisol spikes.
              </p>
            </div>
          </div>

          <!-- Step 5 -->
          <div class="step-card" onclick="toggleStep(this)">
            <div class="step-header">
              <div class="step-number">05</div>
              <div class="time-indicator">45 Seconds</div>
              <h3 class="step-title">Cat-Eye Temple Lift</h3>
              <p class="step-preview">
                Activate brow and temple fascia for an instant "cat-eye" effect.
              </p>
              <div class="expand-indicator">▼</div>
            </div>
            <div class="step-content">
              <p>
                Place index fingers at outer corners of eyes and glide upward
                toward temples.
              </p>
              <p>Hold at the temple and pulse upward 5–6x.</p>
              <p>Repeat 3x per side.</p>
              <p style="margin-top: 15px; font-style: italic; color: #d4af37">
                Activates brow and temple fascia for an instant "cat-eye"
                effect.
              </p>
            </div>
          </div>

          <!-- Step 6 -->
          <div class="step-card dark" onclick="toggleStep(this)">
            <div class="step-header">
              <div class="step-number">06</div>
              <div class="time-indicator">1 Minute</div>
              <h3 class="step-title">Cheekbone & Jaw Sculpt</h3>
              <p class="step-preview">
                Sculpt and define facial contours while draining excess fluid.
              </p>
              <div class="expand-indicator">▼</div>
            </div>
            <div class="step-content">
              <p>
                <strong style="color: #d4af37"
                  >Using knuckles or a gua-sha tool:</strong
                >
              </p>
              <ol style="color: #ffffff; padding-left: 20px; margin-top: 15px">
                <li>
                  Glide from chin up along the jawline to the ear 5x each side
                </li>
                <li>
                  Sweep from nostril outward along the cheekbone to the temple
                  5x each side
                </li>
              </ol>
              <p style="margin-top: 15px; font-style: italic; color: #d4af37">
                Firms the jaw and cheek areas while draining fluid and defining
                contours.
              </p>
            </div>
          </div>

          <!-- Step 7 -->
          <div class="step-card" onclick="toggleStep(this)">
            <div class="step-header">
              <div class="step-number">07</div>
              <div class="time-indicator">30 Seconds</div>
              <h3 class="step-title">Eye De-Puffer</h3>
              <p class="step-preview">
                Reduce under-eye puffiness and fluid retention instantly.
              </p>
              <div class="expand-indicator">▼</div>
            </div>
            <div class="step-content">
              <p>
                Tap along the orbital bone (inner to outer corner) 10x with your
                ring finger.
              </p>
              <p>
                Finish with a chilled spoon or roller under each eye for 30
                seconds.
              </p>
              <p style="margin-top: 15px; font-style: italic; color: #d4af37">
                Constricts blood vessels and reduces fluid retention under the
                eyes.
              </p>
            </div>
          </div>

          <!-- Step 8 -->
          <div class="step-card dark" onclick="toggleStep(this)">
            <div class="step-header">
              <div class="step-number">08</div>
              <div class="time-indicator">1-2 Minutes</div>
              <h3 class="step-title">Collagen Activation</h3>
              <p class="step-preview">
                Lock in the lift with Tri-CollagenLift™ products for lasting
                results.
              </p>
              <div class="expand-indicator">▼</div>
            </div>
            <div class="step-content">
              <p>
                Apply the
                <strong style="color: #d4af37"
                  >Tri-CollagenLift™ Activate Serum</strong
                >
                to clean skin, using upward pressing motions.
              </p>
              <p>
                Follow with the
                <strong style="color: #d4af37">Sculpt Cream</strong>:
              </p>
              <p style="margin-top: 15px; color: #ffffff">
                These formulas deliver multi-weight hyaluronic acids, peptides,
                and plant actives that trigger collagen production and
                elasticity recovery.
              </p>
              <p style="margin-top: 15px; font-style: italic; color: #d4af37">
                The products "lock in" the lift biochemically.
              </p>
              <p style="margin-top: 20px; font-weight: bold; color: #d4af37">
                Final move: Smile softly, roll your shoulders back again, and
                step into your day.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Synergy Section -->
    <section id="synergy" class="section section-dark full-width">
      <div class="container">
        <h2 class="section-title fade-in">The Secret to Lasting Results</h2>

        <div class="magazine-split">
          <div class="split-content slide-in-left">
            <p class="luxury-quote" style="color: #d4af37">
              Pair CathamLift™ with Tri-CollagenLift™
            </p>

            <p
              style="
                font-size: 1.2rem;
                line-height: 1.8;
                margin-bottom: 40px;
                color: #ffffff;
              "
            >
              You've just undone stress-aging in 5–6 minutes. But stress is
              relentless. To keep the lift, you need to fortify your skin from
              the inside out.
            </p>

            <div class="highlight-box dark">
              <p style="font-size: 1.2rem; margin-bottom: 30px; color: #ffffff">
                That's what the
                <strong style="color: #d4af37"
                  >Tri-CollagenLift™ Executive Firmness SkinLift Ritual</strong
                >
                is designed for:
              </p>

              <div
                style="
                  display: grid;
                  grid-template-columns: 1fr;
                  gap: 20px;
                  margin: 30px 0;
                "
              >
                <div
                  style="
                    text-align: left;
                    padding: 20px;
                    border-left: 3px solid #d4af37;
                  "
                >
                  <h3
                    style="
                      color: #d4af37;
                      margin-bottom: 10px;
                      font-size: 1.3rem;
                    "
                  >
                    Activate
                  </h3>
                  <p style="color: #ffffff">
                    Rebuilds collagen and restores elasticity.
                  </p>
                </div>
                <div
                  style="
                    text-align: left;
                    padding: 20px;
                    border-left: 3px solid #d4af37;
                  "
                >
                  <h3
                    style="
                      color: #d4af37;
                      margin-bottom: 10px;
                      font-size: 1.3rem;
                    "
                  >
                    Protect
                  </h3>
                  <p style="color: #ffffff">
                    Defends against oxidative and environmental stressors.
                  </p>
                </div>
                <div
                  style="
                    text-align: left;
                    padding: 20px;
                    border-left: 3px solid #d4af37;
                  "
                >
                  <h3
                    style="
                      color: #d4af37;
                      margin-bottom: 10px;
                      font-size: 1.3rem;
                    "
                  >
                    Balance
                  </h3>
                  <p style="color: #ffffff">
                    Calms inflammation, hydrates deeply, strengthens the skin
                    barrier, and locks in results for long-lasting firmness.
                  </p>
                </div>
              </div>
            </div>

            <div style="margin: 50px 0; text-align: center">
              <p
                style="
                  font-size: 1.3rem;
                  margin-bottom: 20px;
                  font-weight: 400;
                  color: #d4af37;
                "
              >
                The synergy:
              </p>
              <p style="font-size: 1.2rem; margin-bottom: 15px; color: #ffffff">
                <strong style="color: #d4af37">CathamLift™</strong> lifts the
                surface.
                <strong style="color: #d4af37">Tri-CollagenLift™</strong>
                awakens cellular youth.
              </p>
              <p style="font-size: 1.1rem; font-style: italic; color: #d4af37">
                The combination compounds over time.
              </p>
            </div>

            <div class="two-column" style="margin-top: 40px">
              <div style="text-align: center">
                <h4 style="color: #d4af37; margin-bottom: 10px">
                  One week in:
                </h4>
                <p style="color: #ffffff">your face looks fresher.</p>
              </div>
              <div style="text-align: center">
                <h4 style="color: #d4af37; margin-bottom: 10px">
                  One month in:
                </h4>
                <p style="color: #ffffff">your jawline looks defined again.</p>
              </div>
            </div>
          </div>

          <div class="split-image slide-in-right">
            <div class="illustration-placeholder">
              <div style="text-align: center; color: #666">
                <strong>Synergy Illustration</strong><br />
                <small
                  >Before/after timeline<br />
                  (1 week vs 1 month results)</small
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Interactive Checklist -->
    <section id="checklist" class="section section-beige full-width">
      <div class="container">
        <h2 class="section-title fade-in">The CathamLift™ Checklist</h2>
        <p class="section-subtitle fade-in">
          Save or Print – Do this every single day
        </p>

        <div class="interactive-checklist">
          <div class="checklist-item" onclick="toggleCheck(this)">
            <div class="checkbox"></div>
            <span class="checklist-text">Shoulder Blade Squeeze (30s)</span>
          </div>
          <div class="checklist-item" onclick="toggleCheck(this)">
            <div class="checkbox"></div>
            <span class="checklist-text">Shoulder Stretch (30s)</span>
          </div>
          <div class="checklist-item" onclick="toggleCheck(this)">
            <div class="checkbox"></div>
            <span class="checklist-text"
              >Suboccipital Pressure Point Release (30s)</span
            >
          </div>
          <div class="checklist-item" onclick="toggleCheck(this)">
            <div class="checkbox"></div>
            <span class="checklist-text">Neck & Shoulder Release (30s)</span>
          </div>
          <div class="checklist-item" onclick="toggleCheck(this)">
            <div class="checkbox"></div>
            <span class="checklist-text">Lymph Activation Taps (1 min)</span>
          </div>
          <div class="checklist-item" onclick="toggleCheck(this)">
            <div class="checkbox"></div>
            <span class="checklist-text"
              >Face-Yoga Neck & Jaw Stretch (45s)</span
            >
          </div>
          <div class="checklist-item" onclick="toggleCheck(this)">
            <div class="checkbox"></div>
            <span class="checklist-text">Chin Press Lift (1 min)</span>
          </div>
          <div class="checklist-item" onclick="toggleCheck(this)">
            <div class="checkbox"></div>
            <span class="checklist-text">Cat-Eye Temple Lift (45s)</span>
          </div>
          <div class="checklist-item" onclick="toggleCheck(this)">
            <div class="checkbox"></div>
            <span class="checklist-text">Cheekbone & Jaw Sculpt (1 min)</span>
          </div>
          <div class="checklist-item" onclick="toggleCheck(this)">
            <div class="checkbox"></div>
            <span class="checklist-text">Eye De-Puffer (30s)</span>
          </div>
          <div class="checklist-item" onclick="toggleCheck(this)">
            <div class="checkbox"></div>
            <span class="checklist-text"
              >Tri-CollagenLift™ Serum + Sculpt Cream (1–2 min)</span
            >
          </div>
        </div>

        <div style="text-align: center; margin-top: 60px">
          <p class="luxury-quote" style="color: #000000">
            It's the highest-impact, lowest-effort 6 minutes you'll spend all
            day.
          </p>
          <a
            href="#"
            class="cta-button download-button"
            style="margin-top: 30px"
          >
            Download PDF Checklist
          </a>
        </div>
      </div>
    </section>

    <!-- Final CTA -->
    <section id="cta" class="cta-section full-width">
      <div class="container">
        <div class="cta-content">
          <h2
            style="
              font-size: 3.5rem;
              font-weight: 300;
              margin-bottom: 30px;
              letter-spacing: 2px;
            "
          >
            Want the Full Advantage?
          </h2>
          <p
            style="
              font-size: 1.4rem;
              margin-bottom: 25px;
              opacity: 0.9;
              color: #d4af37;
            "
          >
            Your guide is just the start.
          </p>
          <p
            style="
              font-size: 1.2rem;
              margin-bottom: 40px;
              max-width: 600px;
              margin-left: auto;
              margin-right: auto;
              line-height: 1.7;
            "
          >
            Unlock the Tri-CollagenLift™ Bundle and experience what happens when
            stress-aging meets its match.
          </p>
          <a href="#" class="cta-button">Discover Tri-CollagenLift™</a>
          <a href="#" class="cta-button download-button">Download PDF Guide</a>
        </div>
      </div>
    </section>

    <script>
      // Interactive checklist functionality
      function toggleCheck(item) {
        const checkbox = item.querySelector(".checkbox");
        checkbox.classList.toggle("checked");

        // Add subtle animation
        item.style.transform = "scale(1.02)";
        setTimeout(() => {
          item.style.transform = "scale(1)";
        }, 150);
      }

      // Step card accordion functionality
      function toggleStep(card) {
        const isExpanded = card.classList.contains("expanded");

        // Close all other cards
        document
          .querySelectorAll(".step-card.expanded")
          .forEach((otherCard) => {
            if (otherCard !== card) {
              otherCard.classList.remove("expanded");
            }
          });

        // Toggle current card
        card.classList.toggle("expanded");

        // Smooth scroll to card if expanding
        if (!isExpanded) {
          setTimeout(() => {
            card.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
          }, 200);
        }
      }

      // Enhanced scroll animations
      function handleScrollAnimation() {
        const elements = document.querySelectorAll(
          ".fade-in, .slide-in-left, .slide-in-right"
        );
        elements.forEach((element) => {
          const elementTop = element.getBoundingClientRect().top;
          const elementVisible = 150;

          if (elementTop < window.innerHeight - elementVisible) {
            element.classList.add("visible");
          }
        });
      }

      // Progress bar functionality
      function updateProgressBar() {
        const scrollTop = window.pageYOffset;
        const docHeight = document.body.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        document.getElementById("progressBar").style.width =
          scrollPercent + "%";
      }

      // Floating navigation functionality
      function updateFloatingNav() {
        const sections = document.querySelectorAll("section[id]");
        const navItems = document.querySelectorAll(".nav-item");

        let currentSection = "";
        sections.forEach((section) => {
          const sectionTop = section.getBoundingClientRect().top;
          if (sectionTop <= 100) {
            currentSection = section.getAttribute("id");
          }
        });

        navItems.forEach((item) => {
          item.classList.remove("active");
          if (item.getAttribute("data-section") === currentSection) {
            item.classList.add("active");
          }
        });
      }

      // Smooth scroll for navigation
      document.querySelectorAll(".nav-item").forEach((item) => {
        item.addEventListener("click", (e) => {
          e.preventDefault();
          const targetId = item.getAttribute("href");
          const targetSection = document.querySelector(targetId);
          if (targetSection) {
            targetSection.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        });
      });

      // Illustration hover effects
      document
        .querySelectorAll(".illustration-placeholder")
        .forEach((placeholder) => {
          placeholder.addEventListener("mouseenter", function () {
            this.style.transform = "scale(1.05)";
            this.style.borderColor = "#d4af37";
          });

          placeholder.addEventListener("mouseleave", function () {
            this.style.transform = "scale(1)";
            this.style.borderColor = "#ccc";
          });
        });

      // Initialize all functionality
      window.addEventListener("scroll", () => {
        handleScrollAnimation();
        updateProgressBar();
        updateFloatingNav();
      });

      window.addEventListener("load", () => {
        handleScrollAnimation();
        updateProgressBar();
        updateFloatingNav();
      });

      // Add subtle parallax effect to hero section
      window.addEventListener("scroll", () => {
        const scrolled = window.pageYOffset;
        const hero = document.querySelector(".hero-section");
        if (hero) {
          hero.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
      });

      // Luxury loading animation
      window.addEventListener("load", () => {
        document.body.style.opacity = "0";
        document.body.style.transition = "opacity 1s ease";
        setTimeout(() => {
          document.body.style.opacity = "1";
        }, 100);
      });
    </script>
  </body>
</html>
