<style>
  /* <PERSON><PERSON> About Page - Editorial Magazine Style */
  .catham-about {
    color: #000;
    max-width: 100%;
    margin: 0 auto;
    line-height: 1.6;
    letter-spacing: 0.02em;
  }

  /* Brand name styling */
  .catham-about h1.brand-name {
    font-size: 72px;
    letter-spacing: 0.2em;
  }

  /* Tagline styling */
  .catham-about .tagline {
    font-size: 28px;
    letter-spacing: 0.1em;
  }

  /* Make founder quote smaller */
  .catham-about .founder-quote {
    font-size: 13px;
    line-height: 1.5;
  }

  /* Regular body text */
  .catham-about p:not(.tagline):not(.founder-quote) {
    font-size: 12px;
  }

  /* Editorial Header */
  .editorial-header {
    width: 100%;
    height: 100vh;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .editorial-header img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
  }

  /* Mobile image optimization */
  @media (max-width: 768px) {
    .editorial-header {
      background-color: #000;
    }

    .editorial-header img {
      content: url("https://cdn.shopify.com/s/files/1/0909/6560/6735/files/timeless_skin_by_catham.jpg?v=1749190615");
      object-fit: contain;
      object-position: center center;
      width: 100%;
      height: 100%;
      left: 0;
    }

    .catham-brand-text {
      letter-spacing: 8px;
      text-align: center;
      display: block;
    }
  }

  @media (max-width: 480px) {
    .editorial-header {
      background-color: #000;
    }

    .editorial-header img {
      content: url("https://cdn.shopify.com/s/files/1/0909/6560/6735/files/timeless_skin_by_catham.jpg?v=1749190615");
      object-fit: contain;
      object-position: center center;
      width: 100%;
      height: 100%;
      left: 0;
    }

    .catham-brand-text {
      letter-spacing: 5px;
      text-align: center;
      display: block;
    }
  }

  .header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 2;
  }

  .header-content {
    position: relative;
    z-index: 3;
    text-align: center;
    color: #fff;
    max-width: 80%;
  }

  .header-title {
    font-size: 60px;
    line-height: 1;
    margin-bottom: 20px;
    letter-spacing: 0.1em;
    font-weight: 300;
    text-transform: uppercase;
  }

  .header-tagline {
    font-size: 20px;
    letter-spacing: 0.2em;
    font-weight: 300;
    border-top: 1px solid #fff;
    border-bottom: 1px solid #fff;
    padding: 15px 0;
    display: inline-block;
  }

  .catham-brand-text {
    color: rgb(255, 255, 255);
    font-family: "FreeSerif", serif;
    letter-spacing: 25px;
  }

  /* Manifesto Section */
  .manifesto-section {
    padding: 180px 0;
    background-color: #000;
    color: #fff;
  }

  .manifesto-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
  }

  .manifesto-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 80px;
    align-items: center;
  }

  .manifesto-title {
    font-size: 14px;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    writing-mode: vertical-rl;
    transform: rotate(180deg);
    margin: 0 auto;
    height: 70%;
    display: flex;
    align-items: center;
  }

  .manifesto-content {
    font-size: 28px;
    line-height: 1.6;
    font-weight: 300;
  }

  .manifesto-content p {
    margin-bottom: 30px;
  }

  .manifesto-signature {
    font-size: 20px;
    font-style: italic;
    margin-top: 60px;
    text-align: right;
  }

  /* Vision & Mission */
  .vision-mission {
    padding: 0;
  }

  .vision-block {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }

  .vision-image {
    height: 100vh;
    overflow: hidden;
  }

  .vision-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .vision-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 80px;
    background-color: #f8f8f8;
  }

  .vision-title {
    font-size: 60px;
    margin-bottom: 60px;
    position: relative;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .vision-title:after {
    content: "";
    position: absolute;
    bottom: -20px;
    left: 0;
    width: 60px;
    height: 2px;
    background-color: #000;
  }

  .vision-text {
    font-family: "Fira Code", monospace;
    font-size: 16px;
    line-height: 1.8;
  }

  .vision-text p {
    margin-bottom: 20px;
  }

  .mission-block {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }

  .mission-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 80px;
  }

  .mission-title {
    font-size: 60px;
    margin-bottom: 60px;
    position: relative;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .mission-title:after {
    content: "";
    position: absolute;
    bottom: -20px;
    left: 0;
    width: 60px;
    height: 2px;
    background-color: #000;
  }

  .mission-text {
    font-family: "Fira Code", monospace;
    font-size: 16px;
    line-height: 1.8;
  }

  .mission-text p {
    margin-bottom: 20px;
  }

  .mission-image {
    height: 100vh;
    overflow: hidden;
  }

  .mission-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Founder Section */
  .founder-section {
    padding: 180px 0;
    background-color: #000;
    color: #fff;
  }

  .founder-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
  }

  .founder-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
  }

  .founder-image {
    position: relative;
  }

  .founder-polaroid {
    background: #fff;
    padding: 20px 20px 60px;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
    transform: rotate(-3deg);
    width: 80%;
    max-width: 400px;
    margin: 0 auto;
    position: relative;
  }

  .founder-polaroid img {
    width: 100%;
    height: auto;
    filter: grayscale(100%);
    display: block;
  }

  .founder-caption {
    position: absolute;
    bottom: 25px;
    left: 0;
    width: 100%;
    text-align: center;
    font-size: 12px;
    color: #000;
  }

  .founder-content {
    text-align: left;
  }

  .founder-title {
    font-size: 14px;
    letter-spacing: 0.2em;
    margin-bottom: 40px;
    position: relative;
    display: inline-block;
    text-transform: uppercase;
  }

  .founder-title:after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: #fff;
  }

  .founder-quote {
    font-size: 28px;
    line-height: 1.6;
    margin-bottom: 30px;
    text-align: justify;
  }

  .founder-quote p {
    margin-bottom: 25px;
  }

  .founder-name {
    font-size: 13px;
    margin-bottom: 10px;
  }

  .founder-role {
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.1em;
  }

  /* Nordic Origins */
  .nordic-section {
    position: relative;
    height: 100vh;
  }

  .nordic-image {
    height: 100%;
    width: 100%;
  }

  .nordic-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .nordic-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .nordic-content {
    color: #fff;
    max-width: 800px;
    text-align: center;
    padding: 0 40px;
  }

  .nordic-title {
    font-size: 60px;
    margin-bottom: 40px;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .nordic-text {
    font-size: 20px;
    line-height: 1.8;
  }

  .nordic-text p {
    margin-bottom: 20px;
  }

  /* Clean Beauty */
  .clean-beauty {
    padding: 180px 0;
  }

  .clean-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
  }

  .clean-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
  }

  .clean-title-block {
    text-align: right;
  }

  .clean-title {
    font-size: 60px;
    margin-bottom: 40px;
    position: relative;
    display: inline-block;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .clean-title:after {
    content: "";
    position: absolute;
    bottom: -20px;
    right: 0;
    width: 60px;
    height: 2px;
    background-color: #000;
  }

  .clean-intro {
    font-size: 20px;
    line-height: 1.8;
    margin-top: 60px;
  }

  .clean-list {
    list-style: none;
    padding: 0;
  }

  .clean-list li {
    position: relative;
    padding-left: 30px;
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 1.6;
  }

  .clean-list li:before {
    content: "—";
    position: absolute;
    left: 0;
    color: #000;
  }

  /* Certification */
  .certification {
    padding: 180px 0;
    background-color: #f8f8f8;
  }

  .cert-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
  }

  .cert-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 80px;
    align-items: center;
  }

  .cert-content {
    text-align: left;
  }

  .cert-title {
    font-size: 60px;
    margin-bottom: 60px;
    position: relative;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .cert-title:after {
    content: "";
    position: absolute;
    bottom: -20px;
    left: 0;
    width: 60px;
    height: 2px;
    background-color: #000;
  }

  .cert-text {
    font-size: 18px;
    line-height: 1.8;
    margin-bottom: 40px;
  }

  .cert-point {
    margin-bottom: 30px;
  }

  .cert-point-title {
    font-weight: bold;
    margin-bottom: 10px;
  }

  .cert-logo {
    text-align: center;
  }

  .cert-logo img {
    max-width: 100%;
    height: auto;
  }

  /* Page Number Styling */
  .page-number {
    position: fixed;
    bottom: 40px;
    right: 40px;
    font-size: 12px;
    letter-spacing: 0.1em;
    z-index: 100;
    color: #999;
  }

  /* Responsive Adjustments */
  @media (max-width: 1200px) {
    .header-title {
      font-size: 80px;
    }

    .manifesto-grid,
    .founder-grid,
    .clean-grid,
    .cert-grid {
      grid-template-columns: 1fr;
      gap: 60px;
    }

    .manifesto-title {
      writing-mode: horizontal-tb;
      transform: none;
      text-align: center;
      margin-bottom: 40px;
    }

    .vision-block,
    .mission-block {
      grid-template-columns: 1fr;
    }

    .vision-image,
    .mission-image {
      height: 50vh;
    }

    .vision-content,
    .mission-content {
      padding: 60px 40px;
    }

    .vision-title,
    .mission-title,
    .cert-title,
    .clean-title,
    .nordic-title {
      font-size: 48px;
    }

    .manifesto-content,
    .founder-quote {
      font-size: 24px;
    }
  }

  @media (max-width: 768px) {
    .editorial-header {
      height: 100vh;
      min-height: 600px;
    }

    .header-content {
      max-width: 90%;
      padding: 0 20px;
    }

    .header-title {
      font-size: 48px;
      letter-spacing: 0.05em;
      margin-bottom: 15px;
      text-align: center;
    }

    .header-tagline {
      font-size: 16px;
      letter-spacing: 0.15em;
      padding: 12px 0;
    }

    .header-overlay {
      background: rgba(0, 0, 0, 0.4);
    }

    .manifesto-section,
    .founder-section,
    .clean-beauty,
    .certification {
      padding: 100px 0;
    }

    .manifesto-content,
    .founder-quote {
      font-size: 20px;
    }

    .vision-title,
    .mission-title,
    .cert-title,
    .clean-title,
    .nordic-title {
      font-size: 36px;
    }

    .vision-text,
    .mission-text,
    .nordic-text,
    .clean-intro {
      font-size: 14px;
    }

    .vision-content,
    .mission-content {
      padding: 60px 15px;
    }

    .manifesto-container,
    .founder-container,
    .clean-container,
    .cert-container {
      padding: 0 15px;
    }

    .page-number {
      bottom: 20px;
      right: 20px;
    }
  }

  /* Extra small mobile devices */
  @media (max-width: 480px) {
    .editorial-header {
      height: 100vh;
      min-height: 500px;
    }

    .header-content {
      max-width: 95%;
      padding: 0 15px;
    }

    .header-title {
      font-size: 36px;
      letter-spacing: 0.03em;
      margin-bottom: 12px;
    }

    .header-tagline {
      font-size: 14px;
      letter-spacing: 0.1em;
      padding: 10px 0;
    }

    .header-overlay {
      background: rgba(0, 0, 0, 0.3);
    }
  }

  /* Quote sections mobile responsive */
  @media (max-width: 768px) {
    .brand-quote-section,
    .vision-quote-section {
      padding: 60px 0 !important;
    }

    .brand-quote-section div[style*="font-size: 32px"],
    .vision-quote-section div[style*="font-size: 32px"] {
      font-size: 20px !important;
      line-height: 1.3 !important;
      padding: 0 20px !important;
    }

    .brand-quote-section div[style*="font-size: 12px"],
    .vision-quote-section div[style*="font-size: 12px"] {
      font-size: 10px !important;
      letter-spacing: 1px !important;
    }
  }

  @media (max-width: 480px) {
    .brand-quote-section,
    .vision-quote-section {
      padding: 40px 0 !important;
    }

    .brand-quote-section div[style*="font-size: 32px"],
    .vision-quote-section div[style*="font-size: 32px"] {
      font-size: 18px !important;
      line-height: 1.3 !important;
      padding: 0 15px !important;
    }
  }
</style>
<div class="catham-about">
  <!-- Editorial Header -->
  <div class="editorial-header">
    <img
      alt="Cathám - Breaking Beauty Standards"
      src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/90s_icons_inspired_skincare_catham.jpg?v=1749304602"
    />
    <div class="header-overlay" style="background: rgba(0, 0, 0, 0.5)">
      <br />
    </div>
    <div class="header-content">
      <h2><br /></h2>
      <h1 class="header-title">
        <span class="catham-brand-text">Cathám</span>
      </h1>
      <div class="header-tagline">Iconic By Instinct</div>
    </div>
  </div>
  <!-- Manifesto Section -->
  <div class="manifesto-section">
    <div class="manifesto-container">
      <div class="manifesto-header">
        <div class="manifesto-issue">ISSUE 01</div>
        <div class="manifesto-title">MANIFESTO</div>
        <div class="manifesto-line"><br /></div>
        <div class="manifesto-subtitle">THE INSTINCT THAT SPOKE LOUDER</div>
      </div>
      <div class="manifesto-content">
        <div class="manifesto-column left">
          <div class="editorial-number">01</div>
          <p class="manifesto-statement highlight">
            <strong>Exceptional skincare for exceptional women.</strong>
          </p>
          <p class="manifesto-statement highlight">
            You wore a mask for too long—<br
              data-end="736"
              data-start="733"
            />chasing a look that wasn’t you.<br
              data-end="770"
              data-start="767"
            />Until instinct spoke louder.
          </p>
          <div class="editorial-quote">"Unfiltered. Unedited. Original."</div>
          <p class="manifesto-statement">
            <span>The last generation to possess this audacity—&nbsp;</span>
          </p>
          <p class="manifesto-statement">
            <span>The '90s icons who defined an era.&nbsp;</span
            ><span><br />Trends followed them. Never the reverse.&nbsp;</span>
          </p>
          <p class="manifesto-statement">
            <span>They possessed something the rest only imitated.</span>
          </p>
          <p class="manifesto-statement">
            <strong>The Instinct They Trusted.</strong>
          </p>
        </div>
        <div class="manifesto-divider"><br /></div>
        <div class="manifesto-column right">
          <div class="editorial-number">02</div>
          <p class="manifesto-statement">
            <span
              >Cathám was born from that same instinct. This is not for
              everyone. Not for the masses. For the heads, not the tails. For
              those who dare to be first.</span
            >
          </p>
          <p class="manifesto-statement">
            <span>Not trend-led. Not overdone. Timeless.&nbsp;</span>
          </p>
          <p class="manifesto-statement">
            Now, it's your turn.<br />To lift more than just your skin—<br />To
            lift the standard.<br />To lift how you see yourself.
          </p>
          <div class="editorial-callout">
            <strong class="highlight-text"
              >Your face is not a trend. It's a trademark.</strong
            >
          </div>
          <p class="manifesto-statement">&nbsp;</p>
          <div class="manifesto-signature">
            <div class="signature-line"><br /></div>
            <strong>Cathám</strong><br />For the original.<br />For the
            exclusive.<br />Led by the Instinct.
          </div>
        </div>
      </div>
      <div class="manifesto-footer"><span>Welcome to Cathám.</span></div>
    </div>
  </div>
</div>
<style>
  @import url("https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,600;1,400&family=Fira+Code:wght@300;400;500&display=swap");

  .manifesto-section {
    background-color: #000;
    color: #fff;
    padding: 100px 15px;
    font-family: "Cormorant Garamond", serif;
  }

  .manifesto-container {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
  }

  .manifesto-header {
    text-align: center;
    margin-bottom: 80px;
    position: relative;
  }

  .manifesto-issue {
    font-family: "Fira Code", monospace;
    font-size: 12px;
    letter-spacing: 2px;
    color: #888;
    margin-bottom: 10px;
  }

  .manifesto-title {
    font-size: 64px;
    letter-spacing: 8px;
    margin-bottom: 20px;
    font-weight: 300;
  }

  .manifesto-subtitle {
    font-family: "Fira Code", monospace;
    font-size: 12px;
    letter-spacing: 3px;
    color: #888;
    margin-top: 20px;
  }

  .manifesto-line {
    width: 60px;
    height: 1px;
    background-color: #fff;
    margin: 0 auto;
  }

  .manifesto-content {
    display: flex;
    gap: 60px;
    margin-bottom: 80px;
    position: relative;
  }

  .manifesto-column {
    flex: 1;
    position: relative;
  }

  .manifesto-divider {
    width: 1px;
    background-color: rgba(255, 255, 255, 0.2);
    align-self: stretch;
  }

  .editorial-number {
    font-size: 120px;
    color: rgba(255, 255, 255, 0.05);
    position: absolute;
    top: -60px;
    left: -20px;
    z-index: 0;
    font-weight: 300;
  }

  .manifesto-statement {
    font-family: "Fira Code", monospace;
    font-size: 15px;
    line-height: 1.8;
    margin-bottom: 40px;
    color: #ccc;
    position: relative;
    z-index: 1;
  }

  .manifesto-statement strong {
    font-weight: 500;
    color: #fff;
  }

  .highlight {
    font-size: 18px;
    color: #fff;
  }

  .editorial-quote {
    font-size: 24px;
    font-style: italic;
    color: #fff;
    border-left: 2px solid #fff;
    padding-left: 20px;
    margin: 40px 0;
    line-height: 1.4;
  }

  .editorial-callout {
    margin: 40px 0;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
  }

  .highlight-text {
    font-size: 22px;
    color: #fff;
    letter-spacing: 1px;
    line-height: 1.4;
  }

  .manifesto-signature {
    font-family: "Fira Code", monospace;
    font-size: 15px;
    line-height: 1.8;
    margin-top: 80px;
    color: #ccc;
    position: relative;
  }

  .signature-line {
    width: 40px;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.3);
    margin-bottom: 20px;
  }

  .manifesto-signature strong {
    font-size: 24px;
    letter-spacing: 2px;
    color: #fff;
    font-weight: 400;
  }

  .manifesto-footer {
    text-align: center;
    font-size: 22px;
    font-style: italic;
    margin-top: 60px;
    padding-top: 40px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Editorial decorative elements */
  .manifesto-container:before {
    content: "";
    position: absolute;
    top: 40px;
    right: 0;
    width: 80px;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.2);
  }

  .manifesto-container:after {
    content: "";
    position: absolute;
    bottom: 40px;
    left: 0;
    width: 80px;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.2);
  }

  @media (max-width: 768px) {
    .manifesto-content {
      flex-direction: column;
      gap: 60px;
    }

    .manifesto-divider {
      height: 1px;
      width: 100%;
      margin: 20px 0;
    }

    .manifesto-title {
      font-size: 42px;
    }

    .editorial-number {
      font-size: 80px;
      top: -40px;
    }

    .editorial-quote,
    .editorial-callout {
      margin: 30px 0;
    }

    .highlight-text {
      font-size: 18px;
    }
  }
</style>
<div class="catham-about">
  <!-- Vision Section -->
  <div class="vision-mission">
    <div class="vision-block">
      <div class="vision-image">
        <img
          alt="Cathám Vision"
          src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/nordic_skincare_catham_breaking_beauty_standards_1920x.jpg?v=1746035272"
        />
      </div>
      <div class="vision-content">
        <h2 class="vision-title">mission</h2>
        <div class="vision-text">
          <strong
            >Cathám was founded to serve women seeking youthful skin without
            injections - innovating collagen-first skincare that rebuilds from
            the cellular level up.</strong
          >
        </div>
      </div>
    </div>
    <!-- Brand Quote Section -->
    <div
      class="brand-quote-section"
      style="
        background-color: #000;
        color: #fff;
        padding: 80px 0;
        text-align: center;
      "
    >
      <div style="max-width: 800px; margin: 0 auto; padding: 0 40px">
        <div
          style="
            font-size: 32px;
            line-height: 1.4;
            font-style: italic;
            margin-bottom: 30px;
            font-family: 'Old Standard TT', serif;
          "
        >
          "While brands manufacture trends, we "manufacture" legends. Cathám
          doesn't make you beautiful. You already are. We make you
          unforgettable."
        </div>
        <div
          style="
            font-family: 'Fira Code', monospace;
            font-size: 12px;
            letter-spacing: 2px;
            text-transform: uppercase;
            color: #888;
          "
        >
          — The Cathám Standard
        </div>
      </div>
    </div>
    <!-- Mission Section -->
    <div class="mission-block">
      <div class="mission-content">
        <h2 class="mission-title">VISION</h2>
        <div class="mission-text">
          <strong
            >Leading the beauty industry from manufactured perfection to
            <em><span style="text-decoration: underline">EXCEPTIONAL</span></em
            >. Pioneering a new era where skincare is not about correction—but
            restoration.</strong
          >
        </div>
      </div>
      <div class="mission-image">
        <img
          alt="Cathám Mission"
          src="https://catham.eu/cdn/shop/files/natural_skincare_1_1920x.jpg?v=1738142102"
        />
      </div>
    </div>
  </div>
  <!-- Vision Quote Section -->
  <div
    class="vision-quote-section"
    style="
      background-color: #000;
      color: #fff;
      padding: 80px 0;
      text-align: center;
    "
  >
    <div style="max-width: 800px; margin: 0 auto; padding: 0 40px">
      <div
        style="
          font-size: 32px;
          line-height: 1.4;
          font-style: italic;
          margin-bottom: 30px;
          font-family: 'Old Standard TT', serif;
        "
      >
        "We bring the liberation, not only to looks but the way of living. It's
        doing the thing even when you're not liked. Even when you're not
        understood. Even when you stand alone. But you don't have to anymore.
        Because Cathám is with you."
      </div>
      <div
        style="
          font-family: 'Fira Code', monospace;
          font-size: 12px;
          letter-spacing: 2px;
          text-transform: uppercase;
          color: #888;
        "
      >
        — The Cathám philosophy
      </div>
    </div>
  </div>
  <!-- Founder Section -->
  <div class="founder-section">
    <div class="founder-container">
      <div class="founder-grid">
        <div class="founder-image">
          <div style="text-align: start" class="founder-polaroid">
            <img
              style="margin-bottom: 16px; float: none"
              alt="Cathám Founder"
              src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/cathamfounder.png?v=1737998019"
              width="1073"
              height="59"
            />
            <div class="founder-caption">
              <span>The Instinct that started it all</span>
            </div>
          </div>
        </div>
        <div class="founder-content">
          <div class="founder-title">The Founder</div>
          <div class="founder-quote">
            <p class="augment-markdown-paragraph svelte-1edcdk9">
              "In an industry built on conformity, I've always been the outlier.
              Whether showing up as myself or creating a brand that felt
              authentically mine, I never quite fitted the mold. I knew there
              had to be other women equally exhausted with playing it safe.
            </p>
            <p class="augment-markdown-paragraph svelte-1edcdk9">
              The market didn't need another beauty brand, that much was clear.
              But what it desperately needed was
              <strong
                ><span style="text-decoration: underline"
                  >liberation</span
                ></strong
              >. And sometimes, that requires someone to start the conversation.
              Led by the same <em>Instinct</em>. Speaking the message even when
              you face resistance.
            </p>
            <p class="augment-markdown-paragraph svelte-1edcdk9">
              <span
                >While others chase approval, we chase excellence. We don't
                create products for the masses. We create for the ones wanting
                exceptional. You never want to be or look like someone else
                again.</span
              >"
            </p>
            <br />
          </div>
          <div class="founder-name">-Ann-Kristiin Reimann</div>
        </div>
      </div>
    </div>
  </div>
</div>
