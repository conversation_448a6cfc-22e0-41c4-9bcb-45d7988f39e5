<meta charset="UTF-8" />
<meta content="width=device-width, initial-scale=1.0" name="viewport" />
<!-- Google Fonts Import for Cormorant Garamond and Times New Roman -->
<style>
  /* <PERSON>ham About Page - Editorial Magazine Style */
  .catham-about {
    color: #000;
    max-width: 100%;
    margin: 0 auto;
    line-height: 1.6;
    letter-spacing: 0.02em;
  }

  /* Brand name styling */
  .catham-about h1.brand-name {
    font-size: 72px;
    letter-spacing: 0.2em;
  }

  /* Tagline styling */
  .catham-about .tagline {
    font-size: 24px;
    letter-spacing: 0.1em;
  }

  /* Make founder quote smaller */
  .catham-about .founder-quote {
    font-size: 13px;
    line-height: 1.5;
  }

  /* Regular body text */
  .catham-about p:not(.tagline):not(.founder-quote) {
    font-size: 14px; /* Changed from 15px */
  }

  /* Editorial Header */
  .editorial-header {
    width: 100%;
    height: 100vh;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .editorial-header img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
  }

  /* Mobile image optimization */
  @media (max-width: 768px) {
    .editorial-header {
      background-color: #000;
    }
    .editorial-header img {
      content: url("https://cdn.shopify.com/s/files/1/0909/6560/6735/files/timeless_skin_by_catham.jpg?v=1749190615");
      object-fit: contain;
      object-position: center center;
      width: 100%;
      height: 100%;
      left: 0;
    }
    .catham-brand-text {
      letter-spacing: 8px;
      text-align: center;
      display: block;
    }
  }

  @media (max-width: 480px) {
    .editorial-header {
      background-color: #000;
    }
    .editorial-header img {
      content: url("https://cdn.shopify.com/s/files/1/0909/6560/6735/files/timeless_skin_by_catham.jpg?v=1749190615");
      object-fit: contain;
      object-position: center center;
      width: 100%;
      height: 100%;
      left: 0;
    }
    .catham-brand-text {
      letter-spacing: 5px;
      text-align: center;
      display: block;
    }
  }

  .header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 2;
  }

  .header-content {
    position: relative;
    z-index: 3;
    text-align: center;
    color: #fff;
    max-width: 80%;
  }

  .header-title {
    font-size: 65px;
    text-align: center;
    line-height: 1;
    margin-bottom: 5px;
    letter-spacing: 0.1em;
    font-weight: 300;
    text-transform: uppercase;
  }

  .header-tagline {
    font-size: 17.5px; /* Increased from 15px + 4px = 19px */
    letter-spacing: 0.05em;
    font-weight: 300;
    text-align: center;
    padding: 5px 0;
    display: inline-block;
    /* Animation for cinematic word "coming together" */
    animation: letterExpand 1.5s ease-out forwards; /* This is where you can change the animation */
  }

  /* Keyframe animation for cinematic word "coming together" effect */
  @keyframes letterExpand {
    0% {
      opacity: 0;
      letter-spacing: 0.5em; /* Start with wider letter spacing */
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      letter-spacing: 0.05em; /* End at original letter spacing */
      transform: translateY(0);
    }
  }

  .catham-brand-text {
    color: rgb(255, 255, 255);
    font-family: "FreeSerif", serif;
    letter-spacing: 25px;
  }

  /* Manifesto Section */
  .manifesto-section {
    padding: 180px 0;
    background-color: #000;
    color: #fff;
  }

  .manifesto-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
  }

  .manifesto-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 80px;
    align-items: center;
  }

  .manifesto-title {
    font-size: 14px;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    writing-mode: vertical-rl;
    transform: rotate(180deg);
    margin: 0 auto;
    height: 70%;
    display: flex;
    align-items: center;
  }

  .manifesto-content {
    font-size: 28px;
    line-height: 1.6;
    font-weight: 300;
  }

  .manifesto-content p {
    margin-bottom: 30px;
  }

  .manifesto-signature {
    font-size: 20px;
    font-style: italic;
    margin-top: 60px;
    text-align: right;
  }

  /* Vision & Mission */
  .vision-mission {
    padding: 0;
  }

  .vision-block {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }

  .vision-image {
    height: 100vh;
    overflow: hidden;
  }

  .vision-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .vision-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 80px;
    background-color: #f8f8f8;
  }

  .vision-title {
    font-size: 60px;
    margin-bottom: 60px;
    position: relative;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .vision-title:after {
    content: "";
    position: absolute;
    bottom: -20px;
    left: 0;
    width: 60px;
    height: 2px;
    background-color: #000;
  }

  .vision-text {
    font-family: "Times New Roman", serif;
    font-size: 22px; /* Changed from 18px */
    line-height: 1.8;
  }

  .vision-text p {
    margin-bottom: 20px;
  }

  .mission-block {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }

  .mission-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 80px;
  }

  .mission-title {
    font-size: 60px;
    margin-bottom: 60px;
    position: relative;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .mission-title:after {
    content: "";
    position: absolute;
    bottom: -20px;
    left: 0;
    width: 60px;
    height: 2px;
    background-color: #000;
  }

  .mission-text {
    font-family: "Times New Roman", serif;
    font-size: 22px; /* Changed from 18px */
    line-height: 1.8;
  }

  .mission-text p {
    margin-bottom: 20px;
  }

  .mission-image {
    height: 100vh;
    overflow: hidden;
  }

  .mission-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Founder Section */
  .founder-section {
    padding: 180px 0;
    background-color: #000;
    color: #fff;
  }

  .founder-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
  }

  .founder-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
  }

  .founder-image {
    position: relative;
  }

  .founder-polaroid {
    background: #fff;
    padding: 20px 20px 60px;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
    transform: rotate(-3deg);
    width: 80%;
    max-width: 400px;
    margin: 0 auto;
    position: relative;
  }

  .founder-polaroid img {
    width: 100%;
    height: auto;
    filter: grayscale(100%);
    display: block;
  }

  .founder-caption {
    position: absolute;
    bottom: 25px;
    left: 0;
    width: 100%;
    text-align: center;
    font-size: 13px; /* Changed from 14px */
    color: #000;
  }

  .founder-content {
    text-align: left;
  }

  .founder-title {
    font-size: 14px;
    letter-spacing: 0.2em;
    margin-bottom: 40px;
    position: relative;
    display: inline-block;
    text-transform: uppercase;
  }

  .founder-title:after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: #fff;
  }

  .founder-quote {
    font-size: 28px;
    line-height: 1.6;
    margin-bottom: 30px;
    text-align: justify;
  }
  /* New style for the bigger heading within founder-quote */
  .founder-quote h3 {
    font-size: 32px; /* Adjust as needed for "bigger" */
    line-height: 1.4;
    margin-bottom: 20px;
    color: #fff; /* Ensure visibility against dark background */
  }

  .founder-quote p {
    margin-bottom: 25px;
  }

  .founder-name {
    font-size: 14px; /* Changed from 15px */
    margin-bottom: 10px;
  }

  .founder-role {
    font-size: 15px; /* Changed from 16px */
    text-transform: uppercase;
    letter-spacing: 0.1em;
  }

  /* Nordic Origins */
  .nordic-section {
    position: relative;
    height: 100vh;
  }

  .nordic-image {
    height: 100%;
    width: 100%;
  }

  .nordic-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .nordic-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .nordic-content {
    color: #fff;
    max-width: 800px;
    text-align: center;
    padding: 0 40px;
  }

  .nordic-title {
    font-size: 60px;
    margin-bottom: 40px;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .nordic-text {
    font-size: 20px; /* Not small, keep as is */
    line-height: 1.8;
  }

  .nordic-text p {
    margin-bottom: 20px;
  }

  /* Clean Beauty */
  .clean-beauty {
    padding: 180px 0;
  }

  .clean-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
  }

  .clean-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
  }

  .clean-title-block {
    text-align: right;
  }

  .clean-title {
    font-size: 60px;
    margin-bottom: 40px;
    position: relative;
    display: inline-block;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .clean-title:after {
    content: "";
    position: absolute;
    bottom: -20px;
    right: 0;
    width: 60px;
    height: 2px;
    background-color: #000;
  }

  .clean-intro {
    font-size: 20px; /* Not small, keep as is */
    line-height: 1.8;
    margin-top: 60px;
  }

  .clean-list {
    list-style: none;
    padding: 0;
  }

  .clean-list li {
    position: relative;
    padding-left: 30px;
    margin-bottom: 20px;
    font-size: 17px; /* Changed from 18px */
    line-height: 1.6;
  }

  .clean-list li:before {
    content: "—";
    position: absolute;
    left: 0;
    color: #000;
  }

  /* Certification */
  .certification {
    padding: 180px 0;
    background-color: #f8f8f8;
  }

  .cert-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
  }

  .cert-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 80px;
    align-items: center;
  }

  .cert-content {
    text-align: left;
  }

  .cert-title {
    font-size: 60px;
    margin-bottom: 60px;
    position: relative;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .cert-title:after {
    content: "";
    position: absolute;
    bottom: -20px;
    left: 0;
    width: 60px;
    height: 2px;
    background-color: #000;
  }

  .cert-text {
    font-size: 18px; /* Not small, keep as is */
    line-height: 1.8;
    margin-bottom: 40px;
  }

  .cert-point {
    margin-bottom: 30px;
  }

  .cert-point-title {
    font-weight: bold;
    margin-bottom: 10px;
  }

  .cert-logo {
    text-align: center;
  }

  .cert-logo img {
    max-width: 100%;
    height: auto;
  }

  /* Page Number Styling */
  .page-number {
    position: fixed;
    bottom: 40px;
    right: 40px;
    font-size: 13px; /* Changed from 14px */
    letter-spacing: 0.1em;
    z-index: 100;
    color: #999;
  }

  /* Responsive Adjustments */
  @media (max-width: 1200px) {
    .header-title {
      font-size: 80px;
    }
    .manifesto-grid,
    .founder-grid,
    .clean-grid,
    .cert-grid {
      grid-template-columns: 1fr;
      gap: 60px;
    }
    .manifesto-title {
      writing-mode: horizontal-tb;
      transform: none;
      text-align: center;
      margin-bottom: 40px;
    }
    .vision-block,
    .mission-block {
      grid-template-columns: 1fr;
    }
    .vision-image,
    .mission-image {
      height: 50vh;
    }
    .vision-content,
    .mission-content {
      padding: 60px 40px;
    }
    .vision-title,
    .mission-title,
    .cert-title,
    .clean-title,
    .nordic-title {
      font-size: 48px;
    }
    .manifesto-content,
    .founder-quote {
      font-size: 24px;
    }
  }

  @media (max-width: 768px) {
    .editorial-header {
      height: 100vh;
      min-height: 600px;
    }
    .header-content {
      max-width: 90%;
      padding: 0 20px;
    }
    .header-title {
      font-size: 48px;
      letter-spacing: 0.05em;
      margin-bottom: 15px;
      text-align: center;
    }
    .header-tagline {
      font-size: 16px; /* Adjusted for smaller screens */
      letter-spacing: 0.15em;
      padding: 12px 0;
      animation: letterExpand 1.5s ease-out forwards; /* Changed animation */
    }
    .header-overlay {
      background: rgba(0, 0, 0, 0.4);
    }
    .manifesto-section,
    .founder-section,
    .clean-beauty,
    .certification {
      padding: 100px 0;
    }
    .manifesto-content,
    .founder-quote {
      font-size: 20px;
    }
    .vision-title,
    .mission-title,
    .cert-title,
    .clean-title,
    .nordic-title {
      font-size: 36px;
    }
    .vision-text,
    .mission-text,
    .nordic-text,
    .clean-intro {
      font-size: 14px;
    }
    .vision-content,
    .mission-content {
      padding: 60px 15px;
    }
    .manifesto-container,
    .founder-container,
    .clean-container,
    .cert-container {
      padding: 0 15px;
    }
    .page-number {
      bottom: 20px;
      right: 20px;
    }
  }

  /* Extra small mobile devices */
  @media (max-width: 480px) {
    .editorial-header {
      height: 100vh;
      min-height: 500px;
    }
    .header-content {
      max-width: 95%;
      padding: 0 15px;
    }
    .header-title {
      font-size: 36px;
      letter-spacing: 0.03em;
      margin-bottom: 12px;
    }
    .header-tagline {
      font-size: 14px; /* Adjusted for extra small screens */
      letter-spacing: 0.1em;
      padding: 10px 0;
      animation: letterExpand 1.5s ease-out forwards; /* Changed animation */
    }
    .header-overlay {
      background: rgba(0, 0, 0, 0.3);
    }
  }

  /* Additional styles for Manifesto Section */
  .manifesto-section {
    background-color: #000;
    color: #fff;
    padding: 100px 15px;
    font-family: "Cormorant Garamond", serif;
  }
  .manifesto-container {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
  }
  .manifesto-header {
    text-align: center;
    margin-bottom: 80px;
    position: relative;
  }
  .manifesto-issue {
    font-family: "Times New Roman", serif;
    font-size: 13px; /* Changed from 14px */
    letter-spacing: 2px;
    color: #888;
    margin-bottom: 10px;
  }
  .manifesto-title {
    font-size: 64px;
    letter-spacing: 8px;
    margin-bottom: 20px;
    font-weight: 300;
  }
  .manifesto-subtitle {
    font-family: "Times New Roman", serif;
    font-size: 13px; /* Changed from 14px */
    letter-spacing: 3px;
    color: #888;
    margin-top: 20px;
  }
  .manifesto-line {
    width: 60px;
    height: 1px;
    background-color: #fff;
    margin: 0 auto;
  }
  .manifesto-content {
    display: flex;
    gap: 60px;
    margin-bottom: 80px;
    position: relative;
  }
  .manifesto-column {
    flex: 1;
    position: relative;
  }
  .manifesto-divider {
    width: 1px;
    background-color: rgba(255, 255, 255, 0.2);
    align-self: stretch;
  }
  .editorial-number {
    font-size: 120px;
    color: rgba(255, 255, 255, 0.05);
    position: absolute;
    top: -60px;
    left: -20px;
    z-index: 0;
    font-weight: 300;
  }
  .manifesto-statement {
    font-family: "Times New Roman", serif;
    font-size: 16px; /* Changed from 17px */
    line-height: 1.8;
    margin-bottom: 40px;
    color: #ccc;
    position: relative;
    z-index: 1;
  }
  .manifesto-statement strong {
    font-weight: 500;
    color: #fff;
  }
  .highlight {
    font-size: 18px;
    color: #fff;
  }
  .editorial-quote {
    font-size: 20px;
    font-style: italic;
    color: #fff;
    border-left: 2px solid #fff;
    padding-left: 20px;
    margin: 40px 0;
    line-height: 1.4;
  }
  .editorial-callout {
    margin: 40px 0;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
  }
  .highlight-text {
    font-size: 18px; /* Reverted to 18px */
    color: #fff;
    letter-spacing: 1px;
    line-height: 1.4;
  }
  .manifesto-signature {
    font-family: "Times New Roman", serif;
    font-size: 16px; /* Changed from 17px */
    line-height: 1.8;
    margin-top: 80px;
    color: #ccc;
    position: relative;
  }
  .signature-line {
    width: 40px;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.3);
    margin-bottom: 20px;
  }
  .manifesto-signature strong {
    font-size: 24px;
    letter-spacing: 2px;
    color: #fff;
    font-weight: 400;
  }
  .manifesto-footer {
    text-align: center;
    font-size: 22px;
    font-style: italic;
    margin-top: 60px;
    padding-top: 40px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Editorial decorative elements */
  .manifesto-container:before {
    content: "";
    position: absolute;
    top: 40px;
    right: 0;
    width: 80px;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.2);
  }
  .manifesto-container:after {
    content: "";
    position: absolute;
    bottom: 40px;
    left: 0;
    width: 80px;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.2);
  }
  @media (max-width: 768px) {
    .manifesto-content {
      flex-direction: column;
      gap: 60px;
    }
    .manifesto-divider {
      height: 1px;
      width: 100%;
      margin: 20px 0;
    }
    .manifesto-title {
      font-size: 42px;
    }
    .editorial-number {
      font-size: 80px;
      top: -40px;
    }
    .editorial-quote,
    .editorial-callout {
      margin: 30px 0;
    }
    .highlight-text {
      font-size: 18px; /* Reverted to 18px */
    }
  }
</style>
<div class="catham-about">
  <!-- Header -->
  <div class="editorial-header">
    <img
      src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/catham_new_york_skin_treatments.jpg?v=1752315455"
      alt="Cathám New York - Skin Treatments"
    />
    <div style="background: rgba(0, 0, 0, 0.5)" class="header-overlay"></div>
    <div class="header-content">
      <h1 class="header-title">
        <span class="catham-brand-text">Cathám</span>
      </h1>
      <div class="header-tagline">LEADING BEAUTY BEYOND CONFORMITY</div>
    </div>
  </div>

  <!-- Heritage Section -->
  <div class="manifesto-section">
    <div class="manifesto-container">
      <div class="manifesto-header">
        <div class="manifesto-issue">THE GENESIS OF CATHÁM</div>
        <div class="manifesto-title">HERITAGE</div>
        <div class="manifesto-line"></div>
        <div class="manifesto-subtitle">
          ROOTED IN NATURE. REFINED FOR THE CITY.
        </div>
      </div>
      <div class="manifesto-content">
        <div class="manifesto-column left">
          <div class="editorial-number">01</div>
          <div
            class="editorial-number text-9xl text-white text-opacity-5 absolute -top-16 -left-5 font-light"
          >
            <span style="color: rgb(31, 31, 31)">01</span>
          </div>
          <h4
            class="manifesto-statement highlight text-lg text-white leading-relaxed mb-10"
          >
            <strong
              >CATHÁM NEW YORK was founded on a clear premise: To reclaim skin's
              clarity, poise, and collagen-rich vitality from the city's
              relentless claims.&nbsp;</strong
            >
          </h4>
          <div class="editorial-quote">
            "We brought this sovereign purity directly from our North European
            heritage, mastering its resilient botanicals to meet the urban
            frontier's demands."
          </div>
          <div class="image-container" style="margin-bottom: 40px">
            <img
              src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/newyorkcity_beauty.jpg?v=1751479066"
              alt="New York City Beauty"
              style="
                width: 100%;
                height: auto;
                box-shadow: 0 8px 16px rgba(255, 255, 255, 0.2),
                  0 0 10px rgba(255, 255, 255, 0.1);
              "
            />
          </div>
        </div>
        <div class="manifesto-divider"></div>
        <div class="manifesto-column right">
          <!-- Right Column: Added inline styles for flexbox to push content to bottom -->
          <div
            class="manifesto-column right"
            style="
              display: flex;
              flex-direction: column;
              justify-content: space-between;
            "
          >
            <div>
              <div class="editorial-number">02</div>
              <p class="manifesto-statement">
                This vision compelled the creation of the Tri-CollagenLift™
                System, harnessing the potency of plants forged in the harshest
                climates. It's our commitment to create youthful skin through
                the highest certified clean beauty standards.
              </p>
              <p class="manifesto-statement">
                Made for the woman who moves fast, thinks sharp, and still seeks
                quiet purity—every day.<br /><br /><br /><br />
              </p>
            </div>
            <div class="image-container" style="margin-bottom: 40px">
              <img
                src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/nordicnature_2.jpg?v=1751479066"
                alt="Nordic Beauty"
                style="
                  width: 100%;
                  height: auto;
                  box-shadow: 0 8px 16px rgba(255, 255, 255, 0.2),
                    0 0 10px rgba(255, 255, 255, 0.1);
                "
              />
            </div>
          </div>
        </div>
      </div>
      <div class="manifesto-footer">
        Purity is the new luxury in a world of constant exposure &amp;
        pollution.
      </div>
    </div>
  </div>

  <!-- Vision Section -->
  <div class="vision-mission">
    <div class="vision-block">
      <div class="vision-image">
        <img
          src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/catham_vision.jpg?v=1752430068"
          alt="Cathám Vision"
        />
      </div>
      <div class="vision-content">
        <h2 class="vision-title">VISION</h2>
        <h2 class="vision-text">
          We lead the beauty industry back to its true origin: dethroning
          conformity by restoring what's truly
          <em><strong>exceptional</strong></em>
        </h2>
        <!-- New quote formatted as a distinct quote block -->
        <div
          style="
            background-color: #f8f8f8;
            color: #000;
            padding: 40px 0;
            text-align: center;
          "
        >
          <div style="max-width: 800px; margin: 0 auto; padding: 0 40px">
            <div
              style="
                font-size: 32px;
                line-height: 1.4;
                font-style: italic;
                margin-bottom: 30px;
                font-family: 'Old Standard TT', serif;
              "
            >
              "We believe that beauty should be governed by truth, not trends or
              artificial ideals."
            </div>
            <div
              style="
                font-family: 'Times New Roman', serif;
                font-size: 12px;
                letter-spacing: 2px;
                text-transform: uppercase;
                color: #888;
              "
            >
              — The Cathám philosophy
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Manifesto Section (Original one, now after Vision Quote) -->
  <div class="manifesto-section">
    <div class="manifesto-container">
      <div class="manifesto-header">
        <div class="manifesto-issue">FOR THOSE SEEKING EXCEPTIONAL</div>
        <div class="manifesto-title">MANIFESTO</div>
        <div class="manifesto-line"></div>
        <div class="manifesto-subtitle">THE INSTINCT THAT SPOKE LOUDER</div>
      </div>
      <div class="manifesto-content">
        <div class="manifesto-column left">
          <div class="editorial-number">02</div>
          <div
            class="editorial-number text-9xl text-white text-opacity-5 absolute -top-16 -left-5 font-light"
          >
            <span style="color: rgb(31, 31, 31)">01</span>
          </div>
          <h4
            class="manifesto-statement highlight text-lg text-white leading-relaxed mb-10"
          >
            <strong
              ><em>Exceptional </em>isn’t created. It’s revealed.<br /><strong
                >Revealed when you defy worldly standards.</strong
              ></strong
            >
          </h4>
          <div class="editorial-quote">"Unfiltered. Unedited. Original."</div>
          <p class="manifesto-statement">
            It's the unique imprint you leave on the world, a mark forged not by
            imitation, but by an unwavering belief in your own distinct essence
            - what we call <strong><em>the Instinct.</em></strong>
          </p>
          <p class="manifesto-statement">&nbsp;</p>
        </div>
        <div class="manifesto-divider"></div>
        <div class="manifesto-column right">
          <div class="editorial-number">02</div>
          <p class="manifesto-statement">
            <span
              >Cathám was born from that same instinct. This isn't for the
              common crowd. It's for those who have the courage to go
              first.</span
            >
          </p>
          <div class="editorial-callout">
            <span class="highlight-text"
              >Your sovereign presence, led with The Instinct.</span
            >
          </div>
          <p class="manifesto-statement">&nbsp;</p>
          <div class="manifesto-signature">
            <div class="signature-line"></div>
            <strong>CATHÁM</strong><br />For the original.<br />For the
            exceptional.<br />
          </div>
        </div>
      </div>
      <div class="manifesto-footer"><span>Welcome to Cathám.</span></div>
    </div>
  </div>

  <!-- Mission Section -->
  <div class="vision-mission">
    <div class="mission-block">
      <div class="mission-content">
        <h2 class="mission-title">MISSION</h2>
        <h4 class="mission-text">
          To create transformative skincare that rebuilds collagen, defies urban
          aging, and upholds authenticity over trends.
        </h4>
      </div>
      <div class="mission-image">
        <img
          src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/catham_mission.jpg?v=1752430068"
          alt="Cathám Mission"
        />
      </div>
    </div>
  </div>

  <!-- Vision Quote Section (Moved here) -->
  <div
    style="
      background-color: #000;
      color: #fff;
      padding: 80px 0;
      text-align: center;
    "
  >
    <div style="max-width: 800px; margin: 0 auto; padding: 0 40px">
      <div
        style="
          font-size: 32px;
          line-height: 1.4;
          font-style: italic;
          margin-bottom: 30px;
          font-family: 'Old Standard TT', serif;
        "
      >
        "At Cathám, we don’t just boost collagen — we reignite the whole
        structure. Because lasting youth isn't fate; it’s a calculated
        strategy."
      </div>
      <div
        style="
          font-family: 'Times New Roman', serif;
          font-size: 12px;
          letter-spacing: 2px;
          text-transform: uppercase;
          color: #888;
        "
      >
        — The Cathám way
      </div>
    </div>
  </div>

  <!-- Founder Section -->
  <div class="founder-section">
    <div class="founder-container">
      <div class="founder-grid">
        <div class="founder-image">
          <div class="founder-polaroid" style="text-align: start">
            <img
              height="59"
              width="1073"
              src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/cathamfounder.png?v=1737998019"
              alt="Cathám Founder"
              style="margin-bottom: 16px; float: none"
            />
            <div class="founder-caption">
              <span>The Instinct that started it all</span>
            </div>
          </div>
        </div>
        <div class="founder-content">
          <div class="founder-title">The Founder</div>
          <div class="founder-quote">
            <h3>Global Vision Charted The Course.</h3>
            <p>
              Raised in Estonia but fueled by the pulse of the world’s greatest
              cities, I recognized that CATHÁM’s path was already charted to go
              big. The vision — clear and commanding — directed every step I
              took. Today, CATHÁM stands as a brand crafted to meet excellence
              in demands of urban skin with precision and purpose.
            </p>
            <p>
              More than skincare, it is a statement of inner authority and
              refined power — making beauty exceptional and true to you, not by
              the worldy standards. Trends fade, but true instinct guides you to
              lead with clarity and purpose.
            </p>
          </div>
          <div class="founder-name">-Ann-Kristiin Reimann</div>
        </div>
      </div>
    </div>
  </div>
</div>
