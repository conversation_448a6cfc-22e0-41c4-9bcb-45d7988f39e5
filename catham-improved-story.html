<meta charset="UTF-8" />
<meta content="width=device-width, initial-scale=1.0" name="viewport" />
<!-- Google Fonts Import for Cormorant Garamond and Times New Roman -->
<style>
  /* <PERSON>ham About Page - Editorial Magazine Style */
  .catham-about {
    color: #000;
    max-width: 100%;
    margin: 0 auto;
    line-height: 1.6;
    letter-spacing: 0.02em;
  }

  /* Brand name styling */
  .catham-about h1.brand-name {
    font-size: 72px;
    letter-spacing: 0.2em;
  }

  /* Tagline styling */
  .catham-about .tagline {
    font-size: 24px;
    letter-spacing: 0.1em;
  }

  /* Make founder quote smaller */
  .catham-about .founder-quote {
    font-size: 13px;
    line-height: 1.5;
  }

  /* Regular body text */
  .catham-about p:not(.tagline):not(.founder-quote) {
    font-size: 14px; /* Changed from 15px */
  }

  /* Editorial Header */
  .editorial-header {
    width: 100%;
    height: 100vh;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .editorial-header img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
  }

  /* Mobile image optimization */
  @media (max-width: 768px) {
    .editorial-header {
      background-color: #000;
    }
    .editorial-header img {
      content: url("https://cdn.shopify.com/s/files/1/0909/6560/6735/files/timeless_skin_by_catham.jpg?v=1749190615");
      object-fit: contain;
      object-position: center center;
      width: 100%;
      height: 100%;
      left: 0;
    }
    .catham-brand-text {
      letter-spacing: 8px;
      text-align: center;
      display: block;
    }
  }

  @media (max-width: 480px) {
    .editorial-header {
      background-color: #000;
    }
    .editorial-header img {
      content: url("https://cdn.shopify.com/s/files/1/0909/6560/6735/files/timeless_skin_by_catham.jpg?v=1749190615");
      object-fit: contain;
      object-position: center center;
      width: 100%;
      height: 100%;
      left: 0;
    }
    .catham-brand-text {
      letter-spacing: 5px;
      text-align: center;
      display: block;
    }
  }

  .header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 2;
  }

  .header-content {
    position: relative;
    z-index: 3;
    text-align: center;
    color: #fff;
    max-width: 80%;
  }

  .header-title {
    font-size: 65px;
    text-align: center;
    line-height: 1;
    margin-bottom: 5px;
    letter-spacing: 0.1em;
    font-weight: 300;
    text-transform: uppercase;
  }

  .header-tagline {
    font-size: 17.5px; /* Increased from 15px + 4px = 19px */
    letter-spacing: 0.05em;
    font-weight: 300;
    text-align: center;
    padding: 5px 0;
    display: inline-block;
    /* Animation for cinematic word "coming together" */
    animation: letterExpand 1.5s ease-out forwards; /* This is where you can change the animation */
  }

  /* Keyframe animation for cinematic word "coming together" effect */
  @keyframes letterExpand {
    0% {
      opacity: 0;
      letter-spacing: 0.5em; /* Start with wider letter spacing */
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      letter-spacing: 0.05em; /* End at original letter spacing */
      transform: translateY(0);
    }
  }

  .catham-brand-text {
    color: rgb(255, 255, 255);
    font-family: "FreeSerif", serif;
    letter-spacing: 25px;
  }

  /* Manifesto Section */
  .manifesto-section {
    padding: 180px 0;
    background-color: #000;
    color: #fff;
  }

  .manifesto-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
  }

  .manifesto-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 80px;
    align-items: center;
  }

  .manifesto-title {
    font-size: 14px;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    writing-mode: vertical-rl;
    transform: rotate(180deg);
    margin: 0 auto;
    height: 70%;
    display: flex;
    align-items: center;
  }

  .manifesto-content {
    font-size: 28px;
    line-height: 1.6;
    font-weight: 300;
  }

  .manifesto-content p {
    margin-bottom: 30px;
  }

  .manifesto-signature {
    font-size: 20px;
    font-style: italic;
    margin-top: 60px;
    text-align: right;
  }

  /* Vision & Mission */
  .vision-mission {
    padding: 0;
  }

  .vision-block {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }

  .vision-image {
    height: 100vh;
    overflow: hidden;
  }

  .vision-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .vision-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 80px;
    background-color: #f8f8f8;
  }

  .vision-title {
    font-size: 60px;
    margin-bottom: 60px;
    position: relative;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .vision-title:after {
    content: "";
    position: absolute;
    bottom: -20px;
    left: 0;
    width: 60px;
    height: 2px;
    background-color: #000;
  }

  .vision-text {
    font-family: "Times New Roman", serif;
    font-size: 22px; /* Changed from 18px */
    line-height: 1.8;
  }

  .vision-text p {
    margin-bottom: 20px;
  }

  .mission-block {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }

  .mission-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 80px;
  }

  .mission-title {
    font-size: 60px;
    margin-bottom: 60px;
    position: relative;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .mission-title:after {
    content: "";
    position: absolute;
    bottom: -20px;
    left: 0;
    width: 60px;
    height: 2px;
    background-color: #000;
  }

  .mission-text {
    font-family: "Times New Roman", serif;
    font-size: 22px; /* Changed from 18px */
    line-height: 1.8;
  }

  .mission-text p {
    margin-bottom: 20px;
  }

  .mission-image {
    height: 100vh;
    overflow: hidden;
  }

  .mission-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* TRI-COLLAGENLIFT Routine Section */
  .routine-section {
    padding: 120px 0;
    background-color: #f8f8f8;
  }

  .routine-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
  }

  .routine-header {
    text-align: center;
    margin-bottom: 80px;
  }

  .routine-title {
    font-size: 48px;
    margin-bottom: 30px;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: relative;
  }

  .routine-title:after {
    content: "";
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 2px;
    background-color: #000;
  }

  .routine-intro {
    font-family: "Times New Roman", serif;
    font-size: 18px;
    line-height: 1.8;
    max-width: 800px;
    margin: 0 auto;
    color: #333;
  }

  .routine-phases {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    margin: 80px 0;
  }

  .phase-card {
    background: #fff;
    padding: 40px 30px;
    text-align: center;
    border: 2px solid #000;
    position: relative;
  }

  .phase-number {
    font-size: 14px;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    margin-bottom: 20px;
    color: #666;
  }

  .phase-name {
    font-size: 24px;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: 15px;
  }

  .phase-description {
    font-family: "Times New Roman", serif;
    font-size: 14px;
    line-height: 1.6;
    color: #666;
  }

  .products-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-top: 60px;
  }

  .product-item {
    background: #fff;
    padding: 25px;
    border: 1px solid #e0e0e0;
    text-align: left;
  }

  .product-name {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #000;
  }

  .product-phase {
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: #666;
    margin-bottom: 10px;
  }

  .product-benefits {
    font-family: "Times New Roman", serif;
    font-size: 13px;
    line-height: 1.5;
    color: #666;
  }

  .routine-note {
    background: #000;
    color: #fff;
    padding: 40px;
    text-align: center;
    margin-top: 60px;
  }

  .routine-note-text {
    font-family: "Times New Roman", serif;
    font-size: 16px;
    line-height: 1.6;
    font-style: italic;
  }

  /* Founder Section */
  .founder-section {
    padding: 180px 0;
    background-color: #000;
    color: #fff;
  }

  .founder-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
  }

  .founder-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
  }

  .founder-image {
    position: relative;
  }

  .founder-polaroid {
    background: #fff;
    padding: 20px 20px 60px;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
    transform: rotate(-3deg);
    width: 80%;
    max-width: 400px;
    margin: 0 auto;
    position: relative;
  }

  .founder-polaroid img {
    width: 100%;
    height: auto;
    filter: grayscale(100%);
    display: block;
  }

  .founder-caption {
    position: absolute;
    bottom: 25px;
    left: 0;
    width: 100%;
    text-align: center;
    font-size: 13px;
    color: #000;
  }

  .founder-content {
    text-align: left;
  }

  .founder-title {
    font-size: 14px;
    letter-spacing: 0.2em;
    margin-bottom: 40px;
    position: relative;
    display: inline-block;
    text-transform: uppercase;
  }

  .founder-title:after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: #fff;
  }

  .founder-quote {
    font-size: 28px;
    line-height: 1.6;
    margin-bottom: 30px;
    text-align: justify;
  }

  .founder-quote h3 {
    font-size: 32px;
    line-height: 1.4;
    margin-bottom: 20px;
    color: #fff;
  }

  .founder-quote p {
    margin-bottom: 25px;
  }

  .founder-name {
    font-size: 14px;
    margin-bottom: 10px;
  }

  .founder-role {
    font-size: 15px;
    text-transform: uppercase;
    letter-spacing: 0.1em;
  }

  /* Responsive Adjustments */
  @media (max-width: 1200px) {
    .header-title {
      font-size: 80px;
    }
    .manifesto-grid,
    .founder-grid {
      grid-template-columns: 1fr;
      gap: 60px;
    }
    .manifesto-title {
      writing-mode: horizontal-tb;
      transform: none;
      text-align: center;
      margin-bottom: 40px;
    }
    .vision-block,
    .mission-block {
      grid-template-columns: 1fr;
    }
    .vision-image,
    .mission-image {
      height: 50vh;
    }
    .vision-content,
    .mission-content {
      padding: 60px 40px;
    }
    .vision-title,
    .mission-title,
    .routine-title {
      font-size: 48px;
    }
    .manifesto-content,
    .founder-quote {
      font-size: 24px;
    }
    .routine-phases {
      grid-template-columns: 1fr;
      gap: 30px;
    }
    .products-grid {
      grid-template-columns: 1fr;
    }
  }

  @media (max-width: 768px) {
    .editorial-header {
      height: 100vh;
      min-height: 600px;
    }
    .header-content {
      max-width: 90%;
      padding: 0 20px;
    }
    .header-title {
      font-size: 48px;
      letter-spacing: 0.05em;
      margin-bottom: 15px;
      text-align: center;
    }
    .header-tagline {
      font-size: 16px;
      letter-spacing: 0.15em;
      padding: 12px 0;
      animation: letterExpand 1.5s ease-out forwards;
    }
    .header-overlay {
      background: rgba(0, 0, 0, 0.4);
    }
    .manifesto-section,
    .founder-section,
    .routine-section {
      padding: 100px 0;
    }
    .manifesto-content,
    .founder-quote {
      font-size: 20px;
    }
    .vision-title,
    .mission-title,
    .routine-title {
      font-size: 36px;
    }
    .vision-text,
    .mission-text {
      font-size: 14px;
    }
    .vision-content,
    .mission-content {
      padding: 60px 15px;
    }
    .manifesto-container,
    .founder-container,
    .routine-container {
      padding: 0 15px;
    }
  }

  /* Extra small mobile devices */
  @media (max-width: 480px) {
    .editorial-header {
      height: 100vh;
      min-height: 500px;
    }
    .header-content {
      max-width: 95%;
      padding: 0 15px;
    }
    .header-title {
      font-size: 36px;
      letter-spacing: 0.03em;
      margin-bottom: 12px;
    }
    .header-tagline {
      font-size: 14px;
      letter-spacing: 0.1em;
      padding: 10px 0;
      animation: letterExpand 1.5s ease-out forwards;
    }
    .header-overlay {
      background: rgba(0, 0, 0, 0.3);
    }
  }
</style>

<div class="catham-about">
  <!-- Editorial Header -->
  <div class="editorial-header">
    <img
      src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/catham_new_york_skin_treatments.jpg?v=1752315455"
      alt="Cathám New York - Skin Treatments"
    />
    <div style="background: rgba(0, 0, 0, 0.5)" class="header-overlay">
      <br />
    </div>
    <div class="header-content">
      <h1 class="header-title">
        <span class="catham-brand-text">Cathám</span>
      </h1>
      <div class="header-tagline">LEADING BEAUTY BEYOND CONFORMITY</div>
    </div>
  </div>

  <!-- Mission Section -->
  <div class="vision-mission">
    <div class="mission-block">
      <div class="mission-content">
        <h2 class="mission-title">MISSION</h2>
        <h2 class="mission-text">
          Cathám was founded to serve women who seek youthful skin without
          injections—offering collagen-first skincare that rebuilds confidence
          from the cellular level up.
        </h2>
      </div>
      <div class="mission-image">
        <img
          src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/nordic_skincare_catham_breaking_beauty_standards_1920x.jpg?v=1746035272"
          alt="Cathám Mission"
        />
      </div>
    </div>
  </div>

  <!-- Brand Quote Section -->
  <div
    style="
      background-color: #000;
      color: #fff;
      padding: 80px 0;
      text-align: center;
    "
  >
    <div style="max-width: 800px; margin: 0 auto; padding: 0 40px">
      <div
        style="
          font-size: 32px;
          line-height: 1.4;
          font-style: italic;
          margin-bottom: 30px;
          font-family: 'Old Standard TT', serif;
        "
      >
        "While brands manufacture trends, we manufacture legends. Cathám doesn't
        make you beautiful. You already are. We make you unforgettable."
      </div>
      <div
        style="
          font-family: 'Times New Roman', serif;
          font-size: 12px;
          letter-spacing: 2px;
          text-transform: uppercase;
          color: #888;
        "
      >
        — The Cathám Standard
      </div>
    </div>
  </div>

  <!-- Vision Section -->
  <div class="vision-mission">
    <div class="vision-block">
      <div class="vision-image">
        <img
          src="https://catham.eu/cdn/shop/files/natural_skincare_1_1920x.jpg?v=1738142102"
          alt="Cathám Vision"
        />
      </div>
      <div class="vision-content">
        <h2 class="vision-title">VISION</h2>
        <h2 class="vision-text">
          Leading the beauty industry away from manufactured perfection to
          exceptional. Pioneering a new era where skincare is not about
          correction—but restoration.
        </h2>
      </div>
    </div>
  </div>

  <!-- TRI-COLLAGENLIFT Routine Section -->
  <div class="routine-section">
    <div class="routine-container">
      <div class="routine-header">
        <h2 class="routine-title">
          HOW TO BUILD YOUR TRI-COLLAGENLIFT ROUTINE
        </h2>
        <p class="routine-intro">
          You don't need to use all products at once — just ensure your routine
          includes at least one product per phase. That's the secret to collagen
          support that lasts.
        </p>
      </div>

      <div class="routine-phases">
        <div class="phase-card">
          <div class="phase-number">Phase 01</div>
          <div class="phase-name">ACTIVATE</div>
          <div class="phase-description">
            Stimulate cellular renewal and collagen production
          </div>
        </div>
        <div class="phase-card">
          <div class="phase-number">Phase 02</div>
          <div class="phase-name">PROTECT</div>
          <div class="phase-description">
            Shield against environmental damage and UV exposure
          </div>
        </div>
        <div class="phase-card">
          <div class="phase-number">Phase 03</div>
          <div class="phase-name">BALANCE</div>
          <div class="phase-description">
            Restore hydration and maintain skin barrier integrity
          </div>
        </div>
      </div>

      <div class="products-grid">
        <div class="product-item">
          <div class="product-name">Firming Bakuchiol Oil Serum</div>
          <div class="product-phase">Phase 1 • Activate</div>
          <div class="product-benefits">
            Stimulates collagen synthesis with plant-based retinol alternative
          </div>
        </div>
        <div class="product-item">
          <div class="product-name">Collagen Lifting Serum</div>
          <div class="product-phase">Phase 1 • Activate</div>
          <div class="product-benefits">
            Firms and lifts with marine collagen peptides
          </div>
        </div>
        <div class="product-item">
          <div class="product-name">Brightening Vitamin C Serum</div>
          <div class="product-phase">Phase 1 • Activate</div>
          <div class="product-benefits">
            Brightens and protects with stable vitamin C complex
          </div>
        </div>
        <div class="product-item">
          <div class="product-name">Anti-Age Day Cream</div>
          <div class="product-phase">Phase 2 • Protect</div>
          <div class="product-benefits">
            Daily protection with antioxidants and SPF
          </div>
        </div>
        <div class="product-item">
          <div class="product-name">Tinted Sunscreen SPF30</div>
          <div class="product-phase">Phase 2 • Protect</div>
          <div class="product-benefits">
            Broad-spectrum protection with natural coverage
          </div>
        </div>
        <div class="product-item">
          <div class="product-name">Rich Collagen Protection Cream</div>
          <div class="product-phase">Phase 2 • Protect</div>
          <div class="product-benefits">
            Intensive barrier protection for mature skin
          </div>
        </div>
        <div class="product-item">
          <div class="product-name">Anti-Puffiness Gel</div>
          <div class="product-phase">Phase 3 • Balance</div>
          <div class="product-benefits">
            Reduces inflammation and restores skin balance
          </div>
        </div>
        <div class="product-item">
          <div class="product-name">Pure Skin Cleansing Mousse</div>
          <div class="product-phase">Phase 3 • Balance</div>
          <div class="product-benefits">
            Gentle cleansing that maintains skin pH balance
          </div>
        </div>
        <div class="product-item">
          <div class="product-name">Ceramide Hydrating Night Cream</div>
          <div class="product-phase">Phase 3 • Balance</div>
          <div class="product-benefits">
            Overnight repair with ceramide complex
          </div>
        </div>
        <div class="product-item">
          <div class="product-name">Niacinamide Moisturising Gel</div>
          <div class="product-phase">Phase 3 • Balance</div>
          <div class="product-benefits">
            Lightweight hydration with pore-refining benefits
          </div>
        </div>
      </div>

      <div class="routine-note">
        <div class="routine-note-text">
          Each product falls into one or more of the three phases: Activate.
          Protect. Balance.<br />
          To support lasting collagen health, your routine should include all
          three functions — not follow them chronologically.<br /><br />
          <strong>For extra benefits, you can layer serums.</strong>
        </div>
      </div>
    </div>
  </div>

  <!-- Founder Section -->
  <div class="founder-section">
    <div class="founder-container">
      <div class="founder-grid">
        <div class="founder-image">
          <div class="founder-polaroid" style="text-align: start">
            <img
              height="59"
              width="1073"
              src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/cathamfounder.png?v=**********"
              alt="Cathám Founder"
              style="margin-bottom: 16px; float: none"
            />
            <div class="founder-caption">
              <span>The Instinct that started it all</span>
            </div>
          </div>
        </div>
        <div class="founder-content">
          <div class="founder-title">The Founder</div>
          <div class="founder-quote">
            <h3>Global Vision Charted The Course.</h3>
            <p>
              Raised in Estonia but fueled by the pulse of the world's greatest
              cities, I recognized that CATHÁM's path was already charted to go
              big. The vision — clear and commanding — directed every step I
              took. Today, CATHÁM stands as a brand crafted to meet excellence
              in demands of urban skin with precision and purpose.
            </p>
            <p>
              More than skincare, it is a statement of inner authority and
              refined power — making beauty exceptional and true to you, not by
              the worldy standards. Trends fade, but true instinct guides you to
              lead with clarity and purpose.
            </p>
          </div>
          <div class="founder-name">-Ann-Kristiin Reimann</div>
        </div>
      </div>
    </div>
  </div>
</div>
