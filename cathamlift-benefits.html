<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CathamLift™ Benefits | Interactive Section</title>
    <style>
      /* Scoped styles - only affects this specific section */
      .cathamlift-benefits-wrapper {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        background: #000000;
        color: #ffffff;
        line-height: 1.6;
        overflow: hidden;
        min-height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        isolation: isolate;
      }

      .cathamlift-benefits-wrapper *,
      .cathamlift-benefits-wrapper *::before,
      .cathamlift-benefits-wrapper *::after {
        box-sizing: border-box;
      }

      .cathamlift-benefits-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 60px 20px;
        width: 100%;
      }

      /* Main Section */
      .cathamlift-benefits-section {
        text-align: center;
        position: relative;
      }

      .cathamlift-intro-text {
        font-size: 1.3rem;
        font-weight: 300;
        margin-bottom: 50px;
        letter-spacing: 0.5px;
        line-height: 1.7;
        color: #ffffff;
      }

      .cathamlift-intro-text strong {
        font-weight: 600;
        color: #ffffff;
      }

      /* Interactive Cards Container */
      .cathamlift-benefits-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 40px;
        margin-top: 50px;
        perspective: 1000px;
      }

      .cathamlift-benefit-card {
        background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 100%);
        border: 2px solid #ffffff;
        padding: 40px 30px;
        position: relative;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        overflow: hidden;
        min-height: 280px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .cathamlift-benefit-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
        transition: left 0.6s ease;
      }

      .cathamlift-benefit-card:hover::before {
        left: 100%;
      }

      .cathamlift-benefit-card:hover {
        transform: translateY(-10px) scale(1.02);
        border-color: #f5f5f0;
        box-shadow: 0 20px 40px rgba(255, 255, 255, 0.1);
      }

      .cathamlift-benefit-card.cathamlift-instant {
        border-color: #ffffff;
      }

      .cathamlift-benefit-card.cathamlift-cumulative {
        border-color: #f5f5f0;
      }

      .cathamlift-benefit-card:hover.cathamlift-instant {
        background: linear-gradient(135deg, #2a2a2a 0%, #3c3c3c 100%);
        border-color: #ffffff;
      }

      .cathamlift-benefit-card:hover.cathamlift-cumulative {
        background: linear-gradient(135deg, #2a2a2a 0%, #3c3c3c 100%);
        border-color: #f5f5f0;
      }

      .cathamlift-benefit-number {
        font-size: 4rem;
        font-weight: 100;
        opacity: 0.3;
        position: absolute;
        top: 15px;
        right: 20px;
        transition: all 0.3s ease;
      }

      .cathamlift-benefit-card:hover .cathamlift-benefit-number {
        opacity: 0.6;
        transform: scale(1.1);
      }

      .cathamlift-benefit-title {
        font-size: 2.2rem;
        font-weight: 300;
        margin-bottom: 20px;
        text-transform: uppercase;
        letter-spacing: 2px;
        transition: all 0.3s ease;
      }

      .cathamlift-benefit-card:hover .cathamlift-benefit-title {
        transform: translateY(-5px);
      }

      .cathamlift-benefit-description {
        font-size: 1.1rem;
        line-height: 1.6;
        font-weight: 300;
        transition: all 0.3s ease;
      }

      .cathamlift-benefit-card:hover .cathamlift-benefit-description {
        transform: translateY(-3px);
      }

      /* Connecting Line Animation */
      .cathamlift-connection-line {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 2px;
        height: 60px;
        background: linear-gradient(to bottom, #ffffff, transparent);
        transform: translateX(-50%);
        opacity: 0.5;
        animation: cathamlift-pulse 2s infinite;
      }

      @keyframes cathamlift-pulse {
        0%,
        100% {
          opacity: 0.3;
        }
        50% {
          opacity: 0.8;
        }
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .cathamlift-benefits-grid {
          grid-template-columns: 1fr;
          gap: 30px;
        }

        .cathamlift-benefit-card {
          min-height: 220px;
          padding: 30px 25px;
        }

        .cathamlift-benefit-title {
          font-size: 1.8rem;
        }

        .cathamlift-benefit-number {
          font-size: 3rem;
        }

        .cathamlift-intro-text {
          font-size: 1.1rem;
        }

        .cathamlift-connection-line {
          display: none;
        }
      }

      /* Subtle background pattern - scoped to wrapper */
      .cathamlift-benefits-wrapper::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(
            circle at 20% 80%,
            rgba(255, 255, 255, 0.02) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 80% 20%,
            rgba(255, 255, 255, 0.02) 0%,
            transparent 50%
          );
        pointer-events: none;
        z-index: -1;
      }

      /* Interactive hover states */
      .cathamlift-benefit-card {
        position: relative;
        z-index: 1;
      }

      .cathamlift-benefit-card::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          45deg,
          transparent 30%,
          rgba(255, 255, 255, 0.05) 50%,
          transparent 70%
        );
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .cathamlift-benefit-card:hover::after {
        opacity: 1;
      }
    </style>
  </head>
  <body>
    <div class="cathamlift-benefits-wrapper">
      <div class="cathamlift-benefits-container">
        <section class="cathamlift-benefits-section">
          <p class="cathamlift-intro-text">
            <strong>It's science-backed. It's fast.</strong> And when paired
            with our Tri-CollagenLift™ bundle, it works at two levels:
          </p>

          <div class="cathamlift-benefits-grid">
            <div
              class="cathamlift-benefit-card cathamlift-instant"
              onclick="cathamliftAnimateCard(this)"
            >
              <div class="cathamlift-benefit-number">1</div>
              <h3 class="cathamlift-benefit-title">Instant</h3>
              <p class="cathamlift-benefit-description">
                You visibly tighten and de-puff with targeted muscle release and
                drainage.
              </p>
            </div>

            <div
              class="cathamlift-benefit-card cathamlift-cumulative"
              onclick="cathamliftAnimateCard(this)"
            >
              <div class="cathamlift-benefit-number">2</div>
              <h3 class="cathamlift-benefit-title">Cumulative</h3>
              <p class="cathamlift-benefit-description">
                The Tri-CollagenLift™ products rebuild collagen and resilience
                daily.
              </p>
            </div>
          </div>

          <div class="cathamlift-connection-line"></div>
        </section>
      </div>
    </div>

    <script>
      // Scoped JavaScript - only affects CathamLift benefits section
      (function () {
        // Interactive card animation
        window.cathamliftAnimateCard = function (card) {
          card.style.transform = "translateY(-15px) scale(1.05)";
          card.style.borderColor = "#f5f5f0";

          setTimeout(() => {
            card.style.transform = "";
            card.style.borderColor = "";
          }, 300);
        };

        // Add entrance animation
        window.addEventListener("load", function () {
          const cards = document.querySelectorAll(".cathamlift-benefit-card");
          cards.forEach((card, index) => {
            card.style.opacity = "0";
            card.style.transform = "translateY(50px)";

            setTimeout(() => {
              card.style.transition = "all 0.6s ease";
              card.style.opacity = "1";
              card.style.transform = "translateY(0)";
            }, index * 200 + 500);
          });
        });

        // Enhanced hover effects - scoped to cathamlift cards only
        document
          .querySelectorAll(".cathamlift-benefit-card")
          .forEach((card) => {
            card.addEventListener("mouseenter", function () {
              this.style.transform = "translateY(-10px) scale(1.02)";
            });

            card.addEventListener("mouseleave", function () {
              this.style.transform = "translateY(0) scale(1)";
            });
          });
      })();
    </script>
  </body>
</html>
