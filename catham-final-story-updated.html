<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Cathám - The Story</title>
    <style>
      /* Scoped styles for Catham Story only */
      .catham-story * {
        box-sizing: border-box;
      }

      .catham-story {
        font-family: "Times New Roman", serif;
        background: #000;
        color: #fff;
        line-height: 1.6;
        margin: 0;
        padding: 0;
      }

      /* Subtle Animation Styles - Scoped */
      @keyframes catham-fadeInUp {
        0% {
          opacity: 0;
          transform: translateY(30px);
        }
        100% {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .catham-story .animate-fade-in {
        opacity: 0;
        animation: catham-fadeInUp 2s ease-out 0.5s forwards;
      }

      .catham-story .animate-fade-in-delayed {
        opacity: 0;
        animation: catham-fadeInUp 1.5s ease-out 1.5s forwards;
      }

      /* Header Section - Scoped */
      .catham-story .editorial-header {
        width: 100%;
        height: 100vh;
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .catham-story .editorial-header img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
      }

      .catham-story .header-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        z-index: 2;
      }

      .catham-story .header-content {
        position: relative;
        z-index: 3;
        text-align: center;
        color: #fff;
        max-width: 80%;
      }

      .catham-story .header-title {
        font-size: 65px;
        text-align: center;
        line-height: 1;
        margin-bottom: 20px;
        letter-spacing: 0.1em;
        font-weight: 300;
        text-transform: uppercase;
      }

      .catham-story .header-tagline {
        font-size: 17.5px;
        letter-spacing: 0.05em;
        font-weight: 300;
        text-align: center;
        padding: 5px 0;
      }

      .catham-story .catham-brand-text {
        color: rgb(255, 255, 255);
        font-family: "FreeSerif", serif;
        letter-spacing: 25px;
      }

      /* Philosophy Section - Scoped */
      .catham-story .philosophy-section {
        background: #000;
        padding: 200px 0;
        text-align: center;
      }

      .catham-story .philosophy-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 0 80px;
      }

      .catham-story .philosophy-text {
        font-size: 36px;
        line-height: 1.3;
        font-style: italic;
        margin-bottom: 60px;
        font-family: "Old Standard TT", serif;
        color: #fff;
      }

      .catham-story .philosophy-attribution {
        font-family: "Times New Roman", serif;
        font-size: 12px;
        letter-spacing: 2px;
        text-transform: uppercase;
        color: #888;
      }

      /* Heritage Section - Scoped */
      .catham-story .heritage-section {
        background: #000;
        padding: 200px 0;
        text-align: center;
      }

      .catham-story .heritage-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 40px;
      }

      .catham-story .heritage-header {
        margin-bottom: 80px;
      }

      .catham-story .heritage-subtitle {
        font-size: 14px;
        letter-spacing: 0.3em;
        text-transform: uppercase;
        color: #888;
        margin-bottom: 20px;
      }

      .catham-story .heritage-title {
        font-size: 64px;
        letter-spacing: 8px;
        margin-bottom: 20px;
        font-weight: 300;
        color: #fff;
      }

      .catham-story .heritage-line {
        width: 60px;
        height: 1px;
        background-color: #fff;
        margin: 20px auto;
      }

      .catham-story .heritage-tagline {
        font-size: 14px;
        letter-spacing: 3px;
        color: #888;
        margin-top: 20px;
      }

      .catham-story .heritage-text {
        font-size: 20px;
        font-weight: 500;
        line-height: 1.4;
        margin: 60px auto 40px;
        max-width: 800px;
        color: #fff;
      }

      .catham-story .heritage-quote {
        font-size: 20px;
        font-style: italic;
        line-height: 1.4;
        color: #fff;
        margin: 40px auto;
        max-width: 600px;
      }

      /* Vision Section - Scoped */
      .catham-story .vision-section {
        background: #000;
        padding: 0;
      }

      .catham-story .vision-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        min-height: 100vh;
      }

      .catham-story .vision-image {
        position: relative;
        overflow: hidden;
      }

      .catham-story .vision-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        filter: grayscale(20%);
      }

      .catham-story .vision-content {
        padding: 120px 80px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        background: #000;
        color: #fff;
      }

      .catham-story .vision-title {
        font-size: 42px;
        font-weight: 300;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        margin-bottom: 50px;
        position: relative;
        color: #fff;
      }

      .catham-story .vision-title:after {
        content: "";
        position: absolute;
        bottom: -15px;
        left: 0;
        width: 60px;
        height: 2px;
        background: #fff;
      }

      .catham-story .vision-text {
        font-size: 32px;
        font-weight: 500;
        line-height: 1.2;
        margin-bottom: 40px;
        color: #fff;
      }

      .catham-story .vision-description {
        font-size: 18px;
        line-height: 1.6;
        color: #ccc;
      }

      /* Mission Section - Scoped */
      .catham-story .mission-section {
        background: #000;
        padding: 200px 0;
        text-align: center;
      }

      .catham-story .mission-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 0 60px;
      }

      .catham-story .mission-title {
        font-size: 48px;
        font-weight: 300;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        margin-bottom: 60px;
        color: #fff;
      }

      .catham-story .mission-text {
        font-size: 24px;
        font-weight: 300;
        line-height: 1.4;
        color: #fff;
      }

      /* Strategy Quote Section - Scoped */
      .catham-story .strategy-section {
        background: #000;
        padding: 120px 0;
        text-align: center;
      }

      .catham-story .strategy-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 0 60px;
      }

      .catham-story .strategy-quote {
        font-size: 32px;
        font-weight: 300;
        line-height: 1.3;
        font-style: italic;
        margin-bottom: 40px;
        color: #fff;
      }

      .catham-story .strategy-attribution {
        font-size: 12px;
        letter-spacing: 0.2em;
        text-transform: uppercase;
        color: #888;
      }

      /* Founder Section - Scoped */
      .catham-story .founder-section {
        background: #000;
        padding: 0;
      }

      .catham-story .founder-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        min-height: 100vh;
      }

      .catham-story .founder-image {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 80px;
      }

      .catham-story .founder-polaroid {
        background: #fff;
        padding: 20px 20px 60px;
        box-shadow: 0 0 30px rgba(255, 255, 255, 0.1);
        transform: rotate(-2deg);
        max-width: 350px;
        position: relative;
      }

      .catham-story .founder-polaroid img {
        width: 100%;
        height: auto;
        filter: grayscale(100%);
        display: block;
      }

      .catham-story .founder-caption {
        position: absolute;
        bottom: 25px;
        left: 0;
        width: 100%;
        text-align: center;
        font-size: 12px;
        color: #000;
        letter-spacing: 0.1em;
      }

      .catham-story .founder-content {
        padding: 120px 80px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .catham-story .founder-title {
        font-size: 42px;
        font-weight: 300;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        margin-bottom: 40px;
        position: relative;
        color: #fff;
      }

      .catham-story .founder-title:after {
        content: "";
        position: absolute;
        bottom: -15px;
        left: 0;
        width: 60px;
        height: 1px;
        background: #fff;
      }

      .catham-story .founder-quote {
        font-size: 16px;
        line-height: 1.7;
        margin-bottom: 40px;
        color: #ccc;
      }

      .catham-story .founder-quote p {
        margin-bottom: 20px;
      }

      .catham-story .founder-name {
        font-size: 14px;
        letter-spacing: 0.1em;
        font-style: italic;
        color: #888;
      }

      /* NYC Image Section - Scoped */
      .catham-story .nyc-section {
        background: #000;
        padding: 0;
      }

      .catham-story .nyc-image {
        position: relative;
        height: 60vh;
        overflow: hidden;
      }

      .catham-story .nyc-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        filter: grayscale(40%);
      }

      .catham-story .nyc-overlay {
        position: absolute;
        bottom: 40px;
        left: 40px;
        color: #fff;
        z-index: 2;
      }

      .catham-story .nyc-caption {
        font-size: 12px;
        letter-spacing: 0.2em;
        text-transform: uppercase;
        opacity: 0.8;
      }

      /* Responsive Design - Scoped */
      @media (max-width: 768px) {
        .catham-story .header-title {
          font-size: 48px;
        }

        .catham-story .vision-grid,
        .catham-story .founder-grid {
          grid-template-columns: 1fr;
        }

        .catham-story .vision-content,
        .catham-story .founder-content {
          padding: 60px 30px;
        }

        .catham-story .heritage-section,
        .catham-story .mission-section,
        .catham-story .philosophy-section {
          padding: 120px 0;
        }

        .catham-story .heritage-container,
        .catham-story .mission-container,
        .catham-story .strategy-container,
        .catham-story .philosophy-container {
          padding: 0 30px;
        }

        .catham-story .heritage-title {
          font-size: 42px;
        }

        .catham-story .philosophy-text {
          font-size: 28px;
        }

        .catham-story .vision-text {
          font-size: 24px;
        }

        /* Bigger images on mobile */
        .catham-story .vision-image,
        .catham-story .nyc-image {
          height: 70vh;
          min-height: 400px;
        }

        .catham-story .founder-image {
          padding: 60px 40px;
        }

        .catham-story .founder-polaroid {
          max-width: 280px;
        }
      }
    </style>
  </head>
  <body>
    <div class="catham-story">
      <!-- Header -->
      <div class="editorial-header">
        <img
          src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/catham_new_york_skin_treatments.jpg?v=1752315455"
          alt="Cathám New York - Skin Treatments"
        />
        <div class="header-overlay"></div>
        <div class="header-content">
          <h1 class="header-title">
            <span class="catham-brand-text">Cathám</span>
          </h1>
          <div class="header-tagline">LEADING BEAUTY BEYOND CONFORMITY</div>
        </div>
      </div>

      <!-- Core Philosophy Section -->
      <div class="philosophy-section">
        <div class="philosophy-container">
          <div class="philosophy-text animate-fade-in">
            "We believe that beauty should be governed by truth, not trends or
            artificial ideals."
          </div>
          <div class="philosophy-attribution animate-fade-in-delayed">
            — The Cathám philosophy
          </div>
        </div>
      </div>

      <!-- Heritage Section -->
      <div class="heritage-section">
        <div class="heritage-container">
          <div class="heritage-header">
            <div class="heritage-subtitle">THE GENESIS OF CATHÁM</div>
            <div class="heritage-title">HERITAGE</div>
            <div class="heritage-line"></div>
            <div class="heritage-tagline">
              ROOTED IN NATURE. REFINED FOR THE CITY.
            </div>
          </div>

          <div class="heritage-text animate-fade-in">
            CATHÁM NEW YORK was founded on a clear premise: To reclaim skin's
            clarity, poise, and collagen-rich vitality from the city's
            relentless claims.
          </div>

          <div class="heritage-quote animate-fade-in-delayed">
            "We brought this sovereign purity directly from our North European
            heritage, mastering its resilient botanicals to meet the urban
            frontier's demands."
          </div>
        </div>
      </div>

      <!-- NYC Image Section -->
      <div class="nyc-section">
        <div class="nyc-image">
          <img
            src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/newyorkcity_beauty.jpg?v=1751479066"
            alt="New York City Beauty"
          />
          <div class="nyc-overlay">
            <div class="nyc-caption">New York. Refined by Nature.</div>
          </div>
        </div>
      </div>

      <!-- Vision Section -->
      <div class="vision-section">
        <div class="vision-grid">
          <div class="vision-image">
            <img
              src="https://catham.eu/cdn/shop/files/natural_skincare_1_1920x.jpg?v=1738142102"
              alt="Natural Skincare Vision"
            />
          </div>
          <div class="vision-content">
            <div class="vision-title animate-fade-in">VISION</div>
            <div class="vision-text animate-fade-in">
              Leading the beauty industry to its true authority: exceptionalism.
            </div>
            <div class="vision-description animate-fade-in-delayed">
              This exceptionalism is born from an uncompromising belief that
              beauty is an authentic command, not a mere ideal. It's the
              standard we set for lasting, undeniable presence.
            </div>
          </div>
        </div>
      </div>

      <!-- Mission Section -->
      <div class="mission-section">
        <div class="mission-container">
          <div class="mission-title animate-fade-in">MISSION</div>
          <div class="mission-text animate-fade-in-delayed">
            To create transformative skincare that rebuilds collagen, defies
            urban aging, and upholds authenticity over trends.
          </div>
        </div>
      </div>

      <!-- Strategy Quote -->
      <div class="strategy-section">
        <div class="strategy-container">
          <div class="strategy-quote animate-fade-in">
            "At Cathám, we don't just boost collagen — we reignite the whole
            structure. Because lasting youth isn't fate; it's a calculated
            strategy."
          </div>
          <div class="strategy-attribution animate-fade-in-delayed">
            — The Cathám way
          </div>
        </div>
      </div>

      <!-- Founder Section -->
      <div class="founder-section">
        <div class="founder-grid">
          <div class="founder-image">
            <div class="founder-polaroid">
              <img
                src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/cathamfounder.png?v=1737998019"
                alt="Cathám Founder"
              />
              <div class="founder-caption">
                The Instinct that started it all
              </div>
            </div>
          </div>
          <div class="founder-content">
            <div class="founder-title animate-fade-in">The Founder</div>
            <div class="founder-quote animate-fade-in-delayed">
              <p>
                For as long as I can remember, I've been captivated by a kind of
                beauty that doesn't beg to be seen — it simply is. The presence
                unmatched. Not defined by trends, not shaped by the moment — but
                by the rare strength of authenticity.
              </p>
              <p>
                Cathám was born from that Instinct— to craft beauty not as
                ritual, but as rulership. For women who don't chase relevance,
                they shape it. Women who don't ask for power — they are power.
              </p>
            </div>
            <div class="founder-name animate-fade-in-delayed">
              -Ann-Kristiin Reimann
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
