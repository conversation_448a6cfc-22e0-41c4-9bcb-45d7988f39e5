<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Tri-CollagenLift™ System - Cathám</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Red+Hat+Text:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
    <style>
      .tri-collagen-system-wrapper {
        font-family: "Red Hat Text", sans-serif !important;
        background: #ffffff !important;
        color: #000000 !important;
        line-height: 1.6 !important;
        width: 100% !important;
        position: relative !important;
        isolation: isolate !important;
      }

      .tri-collagen-system-wrapper * {
        box-sizing: border-box !important;
      }

      .tri-collagen-system-wrapper .hero-section {
        padding: 80px 40px 60px 40px !important;
        text-align: center !important;
        border-bottom: 1px solid #e0e0e0 !important;
        width: 100% !important;
      }

      .tri-collagen-system-wrapper .hero-title {
        font-family: "Times New Roman", serif !important;
        font-weight: 530 !important;
        font-size: 5rem !important;
        letter-spacing: -2px !important;
        margin-bottom: 15px !important;
        line-height: 1.1 !important;
        color: #000 !important;
      }

      .tri-collagen-system-wrapper .hero-subtitle {
        font-family: "Red Hat Text", sans-serif !important;
        font-weight: 400 !important;
        font-size: 0.8rem !important;
        letter-spacing: 0px !important;
        color: #000000 !important;
        margin-bottom: 40px !important;
        max-width: 900px !important;
        margin-left: auto !important;
        margin-right: auto !important;
        line-height: 1.6 !important;
        text-align: center !important;
        text-transform: uppercase !important;
      }

      .tri-collagen-system-wrapper .hero-image {
        width: 300px !important;
        height: 200px !important;
        object-fit: cover !important;
        border: 1px solid #e0e0e0 !important;
        margin: 40px auto !important;
        display: block !important;
      }

      .tri-collagen-system-wrapper .phases-grid {
        padding: 60px 40px !important;
        width: 100% !important;
      }

      .tri-collagen-system-wrapper .phases-container {
        display: grid !important;
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 40px !important;
        margin-top: 0 !important;
        max-width: 1200px !important;
        margin-left: auto !important;
        margin-right: auto !important;
      }

      .tri-collagen-system-wrapper .phase-card {
        background: #ffffff !important;
        border: 2px solid #000000 !important;
        padding: 40px 30px !important;
        text-align: center !important;
        transition: all 0.3s ease !important;
        position: relative !important;
      }

      .tri-collagen-system-wrapper .phase-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
      }

      .tri-collagen-system-wrapper .phase-number {
        font-family: "Times New Roman", serif !important;
        font-weight: 700 !important;
        font-size: 1.8rem !important;
        margin-bottom: 15px !important;
        color: #000 !important;
      }

      .tri-collagen-system-wrapper .phase-name {
        font-family: "Red Hat Text", sans-serif !important;
        font-weight: 700 !important;
        font-size: 1.1rem !important;
        letter-spacing: 2px !important;
        margin-bottom: 20px !important;
        color: #000 !important;
      }

      .tri-collagen-system-wrapper .phase-description {
        font-size: 0.8rem !important;
        letter-spacing: -0.3px !important;
        line-height: 1.6 !important;
        color: #555 !important;
      }

      .tri-collagen-system-wrapper .results-section {
        padding: 80px 40px !important;
        background: #f8f8f8 !important;
        text-align: center !important;
      }

      .tri-collagen-system-wrapper .results-title {
        font-family: "Times New Roman", serif !important;
        font-weight: 700 !important;
        font-size: 2.5rem !important;
        letter-spacing: -1px !important;
        margin-bottom: 20px !important;
        color: #000 !important;
      }

      .tri-collagen-system-wrapper .results-subtitle {
        font-family: "Red Hat Text", sans-serif !important;
        font-weight: 400 !important;
        font-size: 0.9rem !important;
        letter-spacing: 0px !important;
        color: #666 !important;
        margin-bottom: 60px !important;
        text-transform: uppercase !important;
      }

      .tri-collagen-system-wrapper .results-container {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        gap: 60px !important;
        max-width: 800px !important;
        margin: 0 auto !important;
      }

      .tri-collagen-system-wrapper .result-item {
        text-align: center !important;
      }

      .tri-collagen-system-wrapper .result-image {
        width: 100% !important;
        max-width: 350px !important;
        height: 300px !important;
        object-fit: cover !important;
        border: 1px solid #e0e0e0 !important;
        margin-bottom: 20px !important;
      }

      .tri-collagen-system-wrapper .result-label {
        font-family: "Red Hat Text", sans-serif !important;
        font-weight: 700 !important;
        font-size: 0.9rem !important;
        letter-spacing: 1px !important;
        color: #000 !important;
        text-transform: uppercase !important;
      }

      @media (max-width: 768px) {
        .tri-collagen-system-wrapper .hero-section {
          padding: 60px 20px 40px 20px !important;
        }

        .tri-collagen-system-wrapper .hero-title {
          font-size: 1.8rem !important;
          font-weight: 500 !important;
        }

        .tri-collagen-system-wrapper .hero-image {
          width: 250px !important;
          height: 160px !important;
        }

        .tri-collagen-system-wrapper .phases-grid {
          padding: 40px 20px !important;
        }

        .tri-collagen-system-wrapper .phases-container {
          grid-template-columns: 1fr !important;
          gap: 30px !important;
        }

        .tri-collagen-system-wrapper .phase-card {
          padding: 30px 20px !important;
        }

        .tri-collagen-system-wrapper .results-section {
          padding: 60px 20px !important;
        }

        .tri-collagen-system-wrapper .results-title {
          font-size: 2rem !important;
        }

        .tri-collagen-system-wrapper .results-container {
          grid-template-columns: 1fr !important;
          gap: 40px !important;
        }

        .tri-collagen-system-wrapper .result-image {
          height: 250px !important;
        }
      }
    </style>
  </head>
  <body>
    <div class="tri-collagen-system-wrapper">
      <section class="hero-section">
        <h1 class="hero-title">TRI-COLLAGENLIFT™</h1>
        <p class="hero-subtitle">
         Discover how our <strong>three-phase approach</strong> creates the foundation for <strong>collagen-rich skin structure</strong>.
        </p>
        
      <section class="phases-grid">
        <div class="container">
          <div class="phases-container">
            <div class="phase-card">
              <div class="phase-number">N°1</div>
              <h3 class="phase-name">ACTIVATE</h3>
              <p class="phase-description">
                Collagen renewal begins here. Cellular signaling reawakens,
                stimulating fibroblasts to rebuild the skin's structural matrix
                and improve elasticity.
              </p>
            </div>

            <div class="phase-card">
              <div class="phase-number">N°2</div>
              <h3 class="phase-name">PROTECT</h3>
              <p class="phase-description">
                New collagen is vulnerable. A protective barrier forms against
                UV rays and environmental stressors, reducing collagen
                degradation.
              </p>
            </div>

            <div class="phase-card">
              <div class="phase-number">N°3</div>
              <h3 class="phase-name">BALANCE</h3>
              <p class="phase-description">
                Inflammation disrupts collagen health. This phase calms reactive
                skin and maintains optimal conditions for collagen function.
              </p>
            </div>
          </div>
        </div>
      </section>

      <section class="results-section">
        <h2 class="results-title">TESTED RESULTS</h2>
        <p class="results-subtitle">Clinical transformation evidence</p>

        <div class="results-container">
          <div class="result-item">
            <img
              src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/catham_anti-aging_skin_transformation_before.jpg?v=**********"
              alt="Before Tri-CollagenLift™ Treatment"
              class="result-image"
            />
            <div class="result-label">Before</div>
          </div>

          <div class="result-item">
            <img
              src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/catham_anti-aging_skin_transformation_after.jpg?v=**********"
              alt="After 2 Months of Tri-CollagenLift™ Treatment"
              class="result-image"
            />
            <div class="result-label">After 2 Months</div>
          </div>
        </div>
      </section>
    </div>
  </body>
</html>