<div class="magazine-layout">
  <header class="magazine-masthead">
    <div class="masthead-text">Your Brand | Clean Beauty</div>
    <h1 class="magazine-title">YOUR COLLAGEN ROUTINE</h1>
  </header>

  <section class="product-feature">
    <div class="feature-text-full">
      <p class="feature-lead">The ultimate collagen booster for radiant skin</p>
      <p class="fira-text">
        Our revolutionary formula uses marine peptides and advanced hydration
        technology to restore elasticity...
      </p>
    </div>
  </section>

  <section class="product-details">
    <div class="details-container">
      <div class="system-column">
        <h3 class="column-heading fira-heading">
          <strong>COLLAGEN SYSTEM</strong>
        </h3>

        <div data-phase="1" class="phase-container secondary">
          <div class="phase-header">
            <div class="phase-title-group">
              <h4 class="phase-title">Activate</h4>
              <span class="phase-subtitle">SUPPORT</span>
            </div>
            <div class="phase-number">N°1</div>
          </div>
          <p class="phase-description fira-text">
            Marine peptides stimulate natural collagen production to maintain
            firmness.
          </p>
        </div>

        <div data-phase="2" class="phase-container primary">
          <div class="phase-header">
            <div class="phase-title-group">
              <h4 class="phase-title">Protect</h4>
              <span class="phase-subtitle">PRIMARY FUNCTION</span>
            </div>
            <div class="phase-number">N°2</div>
          </div>
          <p class="phase-description fira-text">
            <strong
              >Protects skin from oxidative stress with antioxidants.</strong
            >
          </p>
        </div>

        <div data-phase="3" class="phase-container secondary">
          <div class="phase-header">
            <div class="phase-title-group">
              <h4 class="phase-title">Balance</h4>
              <span class="phase-subtitle">SUPPORT</span>
            </div>
            <div class="phase-number">N°3</div>
          </div>
          <p class="phase-description fira-text">
            Moisturizing agents strengthen skin barrier and prevent dryness.
          </p>
        </div>
      </div>

      <div class="benefits-column">
        <h3 class="column-heading fira-heading">
          <strong>KEY BENEFITS</strong>
        </h3>
        <ul class="benefits-list">
          <li class="benefit-item">
            <div class="benefit-header">
              <span class="benefit-icon">✦</span>
              <span class="benefit-title">Enhances Skin Firmness</span>
            </div>
            <p class="benefit-description fira-text">
              Improves skin texture and resilience over time.
            </p>
          </li>
          <li class="benefit-item">
            <div class="benefit-header">
              <span class="benefit-icon">✦</span>
              <span class="benefit-title">Hydration Boost</span>
            </div>
            <p class="benefit-description fira-text">
              Maintains moisture balance for a plump complexion.
            </p>
          </li>
        </ul>
      </div>
    </div>
  </section>

  <section class="ingredients-section">
    <h3 class="ingredients-heading fira-heading">
      <strong>FULL INGREDIENTS LIST</strong>
    </h3>
    <p class="ingredients-list fira-text">
      Marine Peptides, Hyaluronic Acid, Aloe Vera, Shea Butter, Jojoba Oil,
      Antioxidants, Parfum
    </p>
  </section>
</div>

<style>
  @import url("https://fonts.googleapis.com/css2?family=Bodoni+Moda:wght@400;600;700&display=swap");
  @import url("https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,600;1,400&display=swap");
  @import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500&display=swap");
  @font-face {
    font-family: "Fira Code";
    src: url("fonts/Fira_Code/static/FiraCode-Regular.ttf") format("truetype");
    font-weight: 400;
    font-style: normal;
  }
  @font-face {
    font-family: "Fira Code";
    src: url("fonts/Fira_Code/static/FiraCode-Medium.ttf") format("truetype");
    font-weight: 500;
    font-style: normal;
  }

  body {
    background-color: #f5f5f5;
    margin: 0;
    padding: 0;
  }

  .magazine-layout {
    max-width: 1000px;
    margin: 0 auto;
    font-family: "Cormorant Garamond", serif;
    color: #111;
    line-height: 1.5;
    padding: 20px;
    background-color: #fff;
  }

  .fira-heading {
    font-family: "Fira Code", monospace;
    letter-spacing: 0.5px;
    font-weight: 500;
    text-transform: uppercase;
  }

  .fira-text {
    font-family: "Fira Code", monospace;
    font-size: 11px !important;
    line-height: 1.7;
    letter-spacing: 0.2px;
  }

  .magazine-masthead {
    margin-bottom: 30px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 15px;
  }

  .masthead-text {
    font-family: "Montserrat", sans-serif;
    font-size: 12px;
    letter-spacing: 1px;
    color: #666;
    margin-bottom: 10px;
    font-weight: 400;
  }

  .magazine-title {
    font-family: "Bodoni Moda", serif;
    font-size: 3.5rem;
    letter-spacing: 1px;
    line-height: 1;
    margin: 0;
    font-weight: 700;
  }

  .feature-text-full {
    padding-top: 10px;
    max-width: 800px;
    margin: 0 auto;
  }

  .feature-lead {
    font-size: 1.5rem;
    line-height: 1.4;
    margin-bottom: 20px;
    font-weight: 600;
  }

  .product-details {
    margin: 30px 0;
    padding-top: 30px;
    border-top: 1px solid #ddd;
  }

  .details-container {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
  }

  .system-column,
  .benefits-column {
    flex: 1;
  }

  .column-heading {
    font-family: "Bodoni Moda", serif;
    font-size: 1.2rem;
    letter-spacing: 1px;
    margin: 0 0 20px 0;
  }

  .phase-container {
    border: 1px solid #ddd;
    padding: 20px;
    margin-bottom: 15px;
  }

  .phase-container.primary {
    background-color: #111;
    color: #fff;
    border-color: #111;
  }

  .phase-container.secondary {
    background-color: #f8f8f8;
    color: #666;
  }

  .phase-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  .phase-title-group {
    display: flex;
    flex-direction: column;
  }

  .phase-title {
    font-family: "Bodoni Moda", serif;
    font-size: 1.1rem;
    margin: 0;
  }

  .phase-subtitle {
    font-family: "Fira Code", monospace;
    font-size: 9px;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    font-weight: 500;
    color: inherit;
  }

  .phase-number {
    font-family: "Fira Code", monospace;
    font-weight: 700;
    font-size: 1.2rem;
    letter-spacing: 1.5px;
  }

  .phase-description {
    font-family: "Fira Code", monospace;
    font-size: 11px;
    letter-spacing: 0.3px;
    line-height: 1.6;
  }

  .benefits-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .benefit-item {
    margin-bottom: 20px;
  }

  .benefit-header {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .benefit-icon {
    font-family: "Fira Code", monospace;
    color: #111;
    font-weight: 700;
    font-size: 1.2rem;
  }

  .benefit-title {
    font-family: "Bodoni Moda", serif;
    font-weight: 600;
    font-size: 1rem;
  }

  .benefit-description {
    font-family: "Fira Code", monospace;
    font-size: 11px;
    color: #666;
    margin-top: 4px;
  }

  .ingredients-section {
    border-top: 1px solid #ddd;
    padding-top: 20px;
  }

  .ingredients-heading {
    font-family: "Bodoni Moda", serif;
    font-weight: 700;
    font-size: 1rem;
    letter-spacing: 1px;
    margin-bottom: 10px;
  }

  .ingredients-list {
    font-family: "Fira Code", monospace;
    font-size: 11px;
    color: #666;
    letter-spacing: 0.3px;
  }
</style>
