<meta charset="UTF-8" />
<meta content="width=device-width, initial-scale=1.0" name="viewport" />
<style>
  /* Cathám Story - Clean Black Editorial Style */
  .catham-story {
    color: #fff;
    font-family: "Times New Roman", serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background: #000;
  }

  /* Header */
  .editorial-header {
    width: 100%;
    height: 100vh;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .editorial-header img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
  }

  .header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 2;
  }

  .header-content {
    position: relative;
    z-index: 3;
    text-align: center;
    color: #fff;
    max-width: 80%;
  }

  .header-title {
    font-size: 65px;
    text-align: center;
    line-height: 1;
    margin-bottom: 5px;
    letter-spacing: 0.1em;
    font-weight: 300;
    text-transform: uppercase;
  }

  .header-tagline {
    font-size: 17.5px;
    letter-spacing: 0.05em;
    font-weight: 300;
    text-align: center;
    padding: 5px 0;
    display: inline-block;
  }

  .catham-brand-text {
    color: rgb(255, 255, 255);
    font-family: "FreeSerif", serif;
    letter-spacing: 25px;
  }

  /* Philosophy Quote Section */
  .philosophy-section {
    padding: 120px 0;
    background: #000;
    text-align: center;
  }

  .philosophy-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 40px;
  }

  .philosophy-text {
    font-size: 32px;
    line-height: 1.4;
    font-style: italic;
    margin-bottom: 30px;
    font-family: "Old Standard TT", serif;
    color: #fff;
  }

  .philosophy-attribution {
    font-family: "Times New Roman", serif;
    font-size: 12px;
    letter-spacing: 2px;
    text-transform: uppercase;
    color: #888;
  }

  /* Heritage Section */
  .heritage-section {
    padding: 200px 0;
    background: #000;
    color: #fff;
  }

  .heritage-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
    text-align: center;
  }

  .heritage-header {
    margin-bottom: 80px;
  }

  .heritage-subtitle {
    font-size: 14px;
    letter-spacing: 0.3em;
    text-transform: uppercase;
    color: #888;
    margin-bottom: 20px;
  }

  .heritage-title {
    font-size: 64px;
    letter-spacing: 8px;
    margin-bottom: 20px;
    font-weight: 300;
    color: #fff;
  }

  .heritage-tagline {
    font-size: 14px;
    letter-spacing: 3px;
    color: #888;
    margin-top: 20px;
  }

  .heritage-line {
    width: 60px;
    height: 1px;
    background-color: #fff;
    margin: 20px auto;
  }

  .heritage-text {
    font-size: 20px;
    font-weight: 500;
    line-height: 1.4;
    margin: 60px auto 40px;
    max-width: 800px;
    color: #fff;
  }

  .heritage-quote {
    font-size: 20px;
    font-style: italic;
    line-height: 1.4;
    color: #fff;
    margin: 40px auto;
    max-width: 600px;
  }

  /* Vision Section - Editorial Layout */
  .vision-section {
    padding: 0;
    background: #000;
  }

  .vision-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 100vh;
  }

  .vision-image {
    position: relative;
    overflow: hidden;
  }

  .vision-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: grayscale(20%);
  }

  .vision-content {
    padding: 120px 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: #000;
    color: #fff;
  }

  .vision-title {
    font-size: 42px;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: 50px;
    position: relative;
    color: #fff;
  }

  .vision-title:after {
    content: "";
    position: absolute;
    bottom: -15px;
    left: 0;
    width: 60px;
    height: 2px;
    background: #fff;
  }

  .vision-text {
    font-size: 28px;
    font-weight: 500;
    line-height: 1.4;
    margin-bottom: 30px;
    color: #fff;
  }

  .vision-description {
    font-size: 18px;
    line-height: 1.6;
    color: #ccc;
  }

  /* Mission Section */
  .mission-section {
    padding: 200px 0;
    background: #000;
    text-align: center;
  }

  .mission-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 60px;
  }

  .mission-title {
    font-size: 48px;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: 60px;
    color: #fff;
  }

  .mission-text {
    font-size: 24px;
    font-weight: 300;
    line-height: 1.4;
    color: #fff;
  }

  /* Strategy Quote Section */
  .strategy-section {
    padding: 120px 0;
    background: #000;
    text-align: center;
  }

  .strategy-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 60px;
  }

  .strategy-quote {
    font-size: 32px;
    font-weight: 300;
    line-height: 1.3;
    font-style: italic;
    margin-bottom: 40px;
    color: #fff;
  }

  .strategy-attribution {
    font-size: 12px;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: #888;
  }

  /* Founder Section - Editorial Layout */
  .founder-section {
    padding: 0;
    background: #000;
    color: #fff;
  }

  .founder-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 100vh;
  }

  .founder-image {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 80px;
  }

  .founder-polaroid {
    background: #fff;
    padding: 20px 20px 60px;
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.1);
    transform: rotate(-2deg);
    max-width: 350px;
    position: relative;
  }

  .founder-polaroid img {
    width: 100%;
    height: auto;
    filter: grayscale(100%);
    display: block;
  }

  .founder-caption {
    position: absolute;
    bottom: 25px;
    left: 0;
    width: 100%;
    text-align: center;
    font-size: 12px;
    color: #000;
    letter-spacing: 0.1em;
  }

  .founder-content {
    padding: 120px 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .founder-title {
    font-size: 42px;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: 40px;
    position: relative;
    color: #fff;
  }

  .founder-title:after {
    content: "";
    position: absolute;
    bottom: -15px;
    left: 0;
    width: 60px;
    height: 1px;
    background: #fff;
  }

  .founder-text {
    font-size: 16px;
    line-height: 1.7;
    text-align: justify;
    margin-bottom: 40px;
    color: #ccc;
  }

  .founder-name {
    font-size: 14px;
    letter-spacing: 0.1em;
    font-style: italic;
    color: #888;
  }

  /* Responsive */
  @media (max-width: 768px) {
    .header-title {
      font-size: 48px;
    }
    .vision-grid,
    .founder-grid {
      grid-template-columns: 1fr;
    }
    .vision-content,
    .founder-content {
      padding: 60px 30px;
    }
    .heritage-section,
    .mission-section {
      padding: 120px 0;
    }
    .heritage-container,
    .mission-container,
    .strategy-container {
      padding: 0 30px;
    }
  }
</style>

<div class="catham-story">
  <!-- Header -->
  <div class="editorial-header">
    <img
      src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/catham_new_york_skin_treatments.jpg?v=1752315455"
      alt="Cathám New York - Skin Treatments"
    />
    <div class="header-overlay"></div>
    <div class="header-content">
      <h1 class="header-title">
        <span class="catham-brand-text">Cathám</span>
      </h1>
      <div class="header-tagline">LEADING BEAUTY BEYOND CONFORMITY</div>
    </div>
  </div>

  <!-- Core Philosophy Section -->
  <div class="philosophy-section">
    <div class="philosophy-container">
      <div class="philosophy-text">
        "We believe that beauty should be governed by truth, not trends or
        artificial ideals."
      </div>
      <div class="philosophy-attribution">— The Cathám philosophy</div>
    </div>
  </div>

  <!-- Heritage Section -->
  <div class="heritage-section">
    <div class="heritage-container">
      <div class="heritage-header">
        <div class="heritage-subtitle">THE GENESIS OF CATHÁM</div>
        <div class="heritage-title">HERITAGE</div>
        <div class="heritage-line"></div>
        <div class="heritage-tagline">
          ROOTED IN NATURE. REFINED FOR THE CITY.
        </div>
      </div>

      <div class="heritage-text">
        CATHÁM NEW YORK was founded on a clear premise: To reclaim skin's
        clarity, poise, and collagen-rich vitality from the city's relentless
        claims.
      </div>

      <div class="heritage-quote">
        "We brought this sovereign purity directly from our North European
        heritage, mastering its resilient botanicals to meet the urban
        frontier's demands."
      </div>
    </div>
  </div>

  <!-- Purity Quote -->
  <div class="philosophy-section">
    <div class="philosophy-container">
      <div class="philosophy-text">
        Purity is the new luxury in a world of constant exposure & pollution.
      </div>
    </div>
  </div>

  <!-- Vision Section -->
  <div class="vision-section">
    <div class="vision-grid">
      <div class="vision-image">
        <img
          src="https://catham.eu/cdn/shop/files/natural_skincare_1_1920x.jpg?v=1738142102"
          alt="Natural Skincare Vision"
        />
      </div>
      <div class="vision-content">
        <div class="vision-title">VISION</div>
        <div class="vision-text">
          Leading the beauty industry to its true authority: exceptionalism.
        </div>
        <div class="vision-description">
          This exceptionalism is born from an uncompromising belief that beauty
          is an authentic command, not a mere ideal. It's the standard we set
          for lasting, undeniable presence.
        </div>
      </div>
    </div>
  </div>

  <!-- Mission Section -->
  <div class="mission-section">
    <div class="mission-container">
      <div class="mission-title">MISSION</div>
      <div class="mission-text">
        To create transformative skincare that rebuilds collagen, defies urban
        aging, and upholds authenticity over trends.
      </div>
    </div>
  </div>

  <!-- Strategy Quote -->
  <div class="strategy-section">
    <div class="strategy-container">
      <div class="strategy-quote">
        "At Cathám, we don't just boost collagen — we reignite the whole
        structure. Because lasting youth isn't fate; it's a calculated
        strategy."
      </div>
      <div class="strategy-attribution">— The Cathám way</div>
    </div>
  </div>

  <!-- Founder Section -->
  <div class="founder-section">
    <div class="founder-grid">
      <div class="founder-image">
        <div class="founder-polaroid">
          <img
            src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/cathamfounder.png?v=1737998019"
            alt="Cathám Founder"
          />
          <div class="founder-caption">The Instinct that started it all</div>
        </div>
      </div>
      <div class="founder-content">
        <div class="founder-title">The Founder</div>
        <div class="founder-text">
          For as long as I can remember, I've been captivated by a kind of
          beauty that doesn't beg to be seen — it simply is. The presence
          unmatched. Not defined by trends, not shaped by the moment — but by
          the rare strength of authenticity.
          <br /><br />
          Cathám was born from that Instinct— to craft beauty not as ritual, but
          as rulership. For women who don't chase relevance, they shape it.
          Women who don't ask for power — they are power.
        </div>
        <div class="founder-name">-Ann-Kristiin Reimann</div>
      </div>
    </div>
  </div>
</div>
