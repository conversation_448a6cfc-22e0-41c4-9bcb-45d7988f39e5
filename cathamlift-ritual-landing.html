<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CathamLift™ Ritual Guide | Cathám</title>
    <style>
      /* Scoped styles for CathamLift Ritual Landing page */
      .cathamlift-landing * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      .cathamlift-landing {
        background: #f2f2f2;
        line-height: 1.6;
        overflow-x: hidden;
        min-height: 100vh;
      }

      .cathamlift-landing .container {
        max-width: 900px;
        margin: 0 auto;
        padding: 60px 40px;
      }

      .cathamlift-landing .header {
        text-align: center;
        margin-bottom: 80px;
        border-bottom: 1px solid #ccc;
        padding-bottom: 40px;
      }

      .cathamlift-landing .title {
        margin-bottom: 20px;
        font-size: 42px;
      }

      .cathamlift-landing .subtitle {
        color: #666;
        font-size: 18px;
        text-transform: uppercase;
        letter-spacing: 2px;
      }

      .cathamlift-landing .section {
        margin-bottom: 60px;
        opacity: 0;
        transform: translateY(30px);
        animation: ritualFadeInUp 0.8s ease forwards;
      }

      .cathamlift-landing .section:nth-child(2) {
        animation-delay: 0.2s;
      }
      .cathamlift-landing .section:nth-child(3) {
        animation-delay: 0.4s;
      }
      .cathamlift-landing .section:nth-child(4) {
        animation-delay: 0.6s;
      }
      .cathamlift-landing .section:nth-child(5) {
        animation-delay: 0.8s;
      }

      @keyframes ritualFadeInUp {
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .cathamlift-landing .section-title {
        margin-bottom: 30px;
        font-size: 28px;
        text-align: center;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .cathamlift-landing .benefits-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 40px;
      }

      .cathamlift-landing .benefit-item {
        background: #fff;
        border: 1px solid #000;
        padding: 25px;
        transition: all 0.3s ease;
      }

      .cathamlift-landing .benefit-item:hover {
        transform: translateY(-2px);
      }

      .cathamlift-landing .benefit-icon {
        margin-bottom: 15px;
        font-size: 18px;
      }

      .cathamlift-landing .benefit-text {
        line-height: 1.6;
      }

      .cathamlift-landing .highlight-box {
        background: #000;
        color: #fff;
        padding: 50px;
        margin: 50px 0;
        text-align: center;
      }

      .cathamlift-landing .highlight-title {
        margin-bottom: 25px;
        font-size: 24px;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .cathamlift-landing .highlight-text {
        font-size: 16px;
        line-height: 1.7;
        max-width: 600px;
        margin: 0 auto;
      }

      .cathamlift-landing .needs-list {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 30px;
        margin: 40px 0;
      }

      .cathamlift-landing .need-item {
        background: #fff;
        border: 1px solid #000;
        padding: 30px;
        text-align: center;
        transition: all 0.3s ease;
      }

      .cathamlift-landing .need-item:hover {
        transform: translateY(-2px);
      }

      .cathamlift-landing .need-icon {
        font-size: 24px;
        margin-bottom: 15px;
      }

      .cathamlift-landing .need-text {
        line-height: 1.6;
      }

      .cathamlift-landing .cta-section {
        background: #fff;
        border: 2px solid #000;
        padding: 50px;
        text-align: center;
        margin: 60px 0;
      }

      .cathamlift-landing .cta-title {
        margin-bottom: 30px;
        font-size: 28px;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .cathamlift-landing .cta-subtitle {
        margin-bottom: 40px;
        color: #666;
        font-size: 16px;
      }

      .cathamlift-landing .email-form {
        max-width: 400px;
        margin: 0 auto;
      }

      .cathamlift-landing .email-input {
        width: 100%;
        padding: 15px;
        border: 1px solid #000;
        margin-bottom: 20px;
        font-size: 16px;
      }

      .cathamlift-landing .submit-btn {
        width: 100%;
        padding: 15px;
        background: #000;
        color: #fff;
        border: none;
        font-size: 16px;
        text-transform: uppercase;
        letter-spacing: 1px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .cathamlift-landing .submit-btn:hover {
        background: #333;
      }

      .cathamlift-landing .brand-footer {
        text-align: center;
        margin-top: 80px;
        padding-top: 40px;
        border-top: 1px solid #ccc;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      @media (max-width: 768px) {
        .cathamlift-landing .container {
          padding: 40px 20px;
        }

        .cathamlift-landing .title {
          font-size: 32px;
        }

        .cathamlift-landing .benefits-grid,
        .cathamlift-landing .needs-list {
          grid-template-columns: 1fr;
        }

        .cathamlift-landing .highlight-box {
          padding: 30px 20px;
        }
      }
    </style>
  </head>
  <body>
    <div class="cathamlift-landing">
      <div class="container">
        <div class="header">
          <h1 class="title">CathamLift™ Ritual</h1>
          <p class="subtitle">Your Daily Liberation Practice</p>
        </div>

        <div class="section">
          <h2 class="section-title">What You'll Get</h2>
          <div class="benefits-grid">
            <div class="benefit-item">
              <div class="benefit-icon">✅</div>
              <div class="benefit-text">A step-by-step guided ritual with clear illustrations</div>
            </div>
            <div class="benefit-item">
              <div class="benefit-icon">✅</div>
              <div class="benefit-text">Designed for daily use — visible results with consistent practice</div>
            </div>
            <div class="benefit-item">
              <div class="benefit-icon">✅</div>
              <div class="benefit-text">Supports lymphatic drainage, muscle toning, and tension release</div>
            </div>
            <div class="benefit-item">
              <div class="benefit-icon">✅</div>
              <div class="benefit-text">Perfect addition before makeup or evening skincare</div>
            </div>
          </div>
        </div>

        <div class="section">
          <div class="highlight-box">
            <h2 class="highlight-title">Why It Works</h2>
            <p class="highlight-text">
              Your face holds stress and fluid — leading to puffiness, dull tone, and fine lines.
              The Catham Lifting Ritual gently activates drainage points, wakes up sleeping muscles, 
              and relaxes tension zones, enhancing skin vitality and definition.
            </p>
          </div>
        </div>

        <div class="section">
          <h2 class="section-title">What You Need</h2>
          <div class="needs-list">
            <div class="need-item">
              <div class="need-icon">✨</div>
              <div class="need-text">Just your fingertips</div>
            </div>
            <div class="need-item">
              <div class="need-icon">✨</div>
              <div class="need-text">A few drops of facial oil (for glide + skin nourishment)</div>
            </div>
            <div class="need-item">
              <div class="need-icon">✨</div>
              <div class="need-text">3 minutes of focused self-care</div>
            </div>
          </div>
          <p style="text-align: center; margin-top: 30px; font-style: italic; color: #666;">
            For best results, use before your Tricollagenlift application.
          </p>
        </div>

        <div class="section">
          <div class="cta-section">
            <h2 class="cta-title">Get the Ritual</h2>
            <p class="cta-subtitle">Enter your email below and unlock the guide instantly.</p>
            <form class="email-form">
              <input type="email" class="email-input" placeholder="Your email address" required>
              <button type="submit" class="submit-btn">Unlock Your Guide</button>
            </form>
          </div>
        </div>

        <div class="brand-footer">
          Cathám — This isn't skincare. This is liberation.
        </div>
      </div>
    </div>
  </body>
</html>
